"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_lib_walineConfig_ts";
exports.ids = ["_ssr_src_lib_walineConfig_ts"];
exports.modules = {

/***/ "(ssr)/./src/lib/walineConfig.ts":
/*!*********************************!*\
  !*** ./src/lib/walineConfig.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearWalineConfigCache: () => (/* binding */ clearWalineConfigCache),\n/* harmony export */   getWalineConfig: () => (/* binding */ getWalineConfig),\n/* harmony export */   getWalineConfigCacheStatus: () => (/* binding */ getWalineConfigCacheStatus),\n/* harmony export */   getWalineLang: () => (/* binding */ getWalineLang),\n/* harmony export */   getWalineServerURL: () => (/* binding */ getWalineServerURL),\n/* harmony export */   preloadWalineConfig: () => (/* binding */ preloadWalineConfig)\n/* harmony export */ });\n// 增强的缓存配置管理\nlet cachedConfig = null;\nlet configPromise = null;\nlet lastFetchTime = 0;\nconst CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存\nconst MAX_RETRIES = 3;\nconst RETRY_DELAY = 1000; // 1秒基础延迟\n/**\n * 延迟函数\n */ function delay(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\n/**\n * 带重试机制的Waline配置获取\n */ async function fetchWalineConfigWithRetry(retryCount = 0) {\n    try {\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), 5000); // 5秒超时\n        const response = await fetch(\"/api/system-config/waline/config\", {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            signal: controller.signal,\n            // 添加缓存策略\n            cache: \"default\"\n        });\n        clearTimeout(timeoutId);\n        if (!response.ok) {\n            throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const data = await response.json();\n        const config = {\n            serverURL: data.server_url,\n            lang: data.lang || \"zh-CN\"\n        };\n        // 验证配置有效性\n        if (!config.serverURL || config.serverURL.trim() === \"\") {\n            throw new Error(\"Invalid server URL received\");\n        }\n        return config;\n    } catch (error) {\n        // 如果还有重试次数，进行重试\n        if (retryCount < MAX_RETRIES) {\n            const delayMs = RETRY_DELAY * Math.pow(2, retryCount); // 指数退避\n            if ( true && process.env.ENABLE_WALINE_DEBUG === \"true\") {\n                console.warn(`Waline配置获取失败，${delayMs}ms后重试 (${retryCount + 1}/${MAX_RETRIES}):`, error);\n            }\n            await delay(delayMs);\n            return fetchWalineConfigWithRetry(retryCount + 1);\n        }\n        // 所有重试都失败了，记录错误并返回默认配置\n        if ( true && process.env.ENABLE_WALINE_DEBUG === \"true\") {\n            console.error(\"获取Waline配置彻底失败，使用默认配置:\", error);\n        }\n        // 返回默认配置作为后备\n        return {\n            serverURL: \"https://waline.jyaochen.cn\",\n            lang: \"zh-CN\"\n        };\n    }\n}\n/**\n * 从后台API获取Waline配置（兼容老版本）\n */ async function fetchWalineConfig() {\n    return fetchWalineConfigWithRetry();\n}\n/**\n * 获取Waline配置（带缓存）\n */ async function getWalineConfig() {\n    const now = Date.now();\n    // 检查缓存是否还有效\n    if (cachedConfig && now - lastFetchTime < CACHE_DURATION) {\n        return cachedConfig;\n    }\n    // 如果正在请求中，等待请求完成\n    if (configPromise) {\n        return configPromise;\n    }\n    // 发起新的请求\n    configPromise = fetchWalineConfigWithRetry();\n    try {\n        const config = await configPromise;\n        cachedConfig = config;\n        lastFetchTime = now;\n        if ( true && process.env.ENABLE_WALINE_DEBUG === \"true\") {\n            console.log(\"Waline配置获取成功:\", config);\n        }\n        return config;\n    } catch (error) {\n        // 请求失败，但不清除旧缓存（如果有的话）\n        if (cachedConfig) {\n            if ( true && process.env.ENABLE_WALINE_DEBUG === \"true\") {\n                console.warn(\"配置获取失败，使用缓存的配置:\", cachedConfig);\n            }\n            return cachedConfig;\n        }\n        // 没有缓存配置，抛出错误\n        throw error;\n    } finally{\n        // 请求完成，清除promise缓存\n        configPromise = null;\n    }\n}\n/**\n * 清除配置缓存（用于配置更新后刷新）\n */ function clearWalineConfigCache() {\n    cachedConfig = null;\n    configPromise = null;\n    lastFetchTime = 0;\n    if ( true && process.env.ENABLE_WALINE_DEBUG === \"true\") {\n        console.log(\"Waline配置缓存已清除\");\n    }\n}\n/**\n * 预加载Waline配置\n * 在用户可能需要评论功能之前预先获取配置\n */ async function preloadWalineConfig() {\n    try {\n        await getWalineConfig();\n    } catch (error) {\n        // 预加载失败不影响主流程\n        if ( true && process.env.ENABLE_WALINE_DEBUG === \"true\") {\n            console.warn(\"Waline配置预加载失败:\", error);\n        }\n    }\n}\n/**\n * 获取配置缓存状态\n */ function getWalineConfigCacheStatus() {\n    const now = Date.now();\n    const cacheAge = now - lastFetchTime;\n    return {\n        isCached: !!cachedConfig,\n        isExpired: cacheAge > CACHE_DURATION,\n        lastFetchTime,\n        cacheAge\n    };\n}\n/**\n * 获取Waline服务器URL\n */ async function getWalineServerURL() {\n    const config = await getWalineConfig();\n    return config.serverURL;\n}\n/**\n * 获取Waline语言设置\n */ async function getWalineLang() {\n    const config = await getWalineConfig();\n    return config.lang;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/walineConfig.ts\n");

/***/ })

};
;