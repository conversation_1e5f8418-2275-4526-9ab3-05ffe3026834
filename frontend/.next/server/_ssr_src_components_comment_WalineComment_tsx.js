"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_comment_WalineComment_tsx";
exports.ids = ["_ssr_src_components_comment_WalineComment_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/comment/WalineComment.tsx":
/*!**************************************************!*\
  !*** ./src/components/comment/WalineComment.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalineComment: () => (/* binding */ WalineComment),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _waline_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @waline/client */ \"(ssr)/./node_modules/@waline/client/dist/slim.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.module.js\");\n/* harmony import */ var _waline_client_style__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @waline/client/style */ \"(ssr)/./node_modules/@waline/client/dist/waline.css\");\n/* __next_internal_client_entry_do_not_use__ WalineComment,default auto */ \n\n\n\n\nfunction WalineComment(props) {\n    const walineInstanceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const mountedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(true);\n    const { resolvedTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_3__.useTheme)();\n    // 安全的销毁函数\n    const safeDestroy = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!mountedRef.current) return;\n        try {\n            if (walineInstanceRef.current) {\n                walineInstanceRef.current.destroy();\n                walineInstanceRef.current = null;\n            }\n        } catch (error) {\n            // 静默处理 AbortError 和其他销毁错误\n            if (error?.name !== \"AbortError\") {\n                console.warn(\"Waline destroy error:\", error);\n            }\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        mountedRef.current = true;\n        if (!containerRef.current) return;\n        // 清理之前的实例\n        safeDestroy();\n        // 延迟初始化以避免竞态条件\n        const timeoutId = setTimeout(()=>{\n            if (!mountedRef.current || !containerRef.current) return;\n            try {\n                walineInstanceRef.current = (0,_waline_client__WEBPACK_IMPORTED_MODULE_2__.init)({\n                    ...props,\n                    el: containerRef.current,\n                    serverURL: \"https://waline.jyaochen.cn\",\n                    dark: resolvedTheme === \"dark\",\n                    locale: {\n                        placeholder: \"Share your thoughts and join the discussion...\",\n                        admin: \"Admin\",\n                        level0: \"Newcomer\",\n                        level1: \"Explorer\",\n                        level2: \"Contributor\",\n                        level3: \"Expert\",\n                        level4: \"Master\",\n                        level5: \"Legend\",\n                        anonymous: \"Anonymous\",\n                        login: \"Sign In\",\n                        logout: \"Sign Out\",\n                        profile: \"Profile\",\n                        nickError: \"Nickname must be at least 3 characters\",\n                        mailError: \"Please enter a valid email address\",\n                        wordHint: \"Please enter your comment\",\n                        sofa: \"Be the first to share your thoughts!\",\n                        submit: \"Publish Comment\",\n                        reply: \"Reply\",\n                        cancelReply: \"Cancel Reply\",\n                        comment: \"Comment\",\n                        refresh: \"Refresh\",\n                        more: \"Load More Comments...\",\n                        preview: \"Preview\",\n                        emoji: \"Emoji\",\n                        uploadImage: \"Upload Image\",\n                        seconds: \"seconds ago\",\n                        minutes: \"minutes ago\",\n                        hours: \"hours ago\",\n                        days: \"days ago\",\n                        now: \"just now\"\n                    },\n                    emoji: [\n                        \"//unpkg.com/@waline/emojis@1.2.0/weibo\",\n                        \"//unpkg.com/@waline/emojis@1.2.0/alus\",\n                        \"//unpkg.com/@waline/emojis@1.2.0/bilibili\",\n                        \"//unpkg.com/@waline/emojis@1.2.0/bmoji\",\n                        \"//unpkg.com/@waline/emojis@1.2.0/qq\",\n                        \"//unpkg.com/@waline/emojis@1.2.0/tieba\",\n                        \"//unpkg.com/@waline/emojis@1.2.0/tw-emoji\",\n                        \"//unpkg.com/@waline/emojis@1.2.0/soul-emoji\"\n                    ],\n                    meta: [\n                        \"nick\",\n                        \"mail\",\n                        \"link\"\n                    ],\n                    requiredMeta: [\n                        \"nick\"\n                    ],\n                    login: \"enable\",\n                    wordLimit: [\n                        0,\n                        1000\n                    ],\n                    pageSize: 10,\n                    lang: \"en-US\",\n                    reaction: true,\n                    imageUploader: false,\n                    texRenderer: false,\n                    search: false,\n                    pageview: true\n                });\n            } catch (error) {\n                if (error?.name !== \"AbortError\") {\n                    console.warn(\"Waline init error:\", error);\n                }\n            }\n        }, 100);\n        return ()=>{\n            mountedRef.current = false;\n            clearTimeout(timeoutId);\n            // 延迟销毁以避免 AbortError\n            setTimeout(()=>{\n                safeDestroy();\n            }, 50);\n        };\n    }, [\n        resolvedTheme,\n        safeDestroy\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!mountedRef.current) return;\n        try {\n            if (walineInstanceRef.current) {\n                walineInstanceRef.current.update(props);\n            }\n        } catch (error) {\n            if (error?.name !== \"AbortError\") {\n                console.warn(\"Waline update error:\", error);\n            }\n        }\n    }, [\n        props.path\n    ]);\n    // 组件卸载时清理\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            mountedRef.current = false;\n            safeDestroy();\n        };\n    }, [\n        safeDestroy\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: props.className\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n        lineNumber: 153,\n        columnNumber: 10\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WalineComment);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9jb21tZW50L1dhbGluZUNvbW1lbnQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFFNkQ7QUFLdEM7QUFDZTtBQUVUO0FBT3RCLFNBQVNNLGNBQWNDLEtBQW9CO0lBQ2hELE1BQU1DLG9CQUFvQk4sNkNBQU1BLENBQXdCO0lBQ3hELE1BQU1PLGVBQWVQLDZDQUFNQSxDQUFpQjtJQUM1QyxNQUFNUSxhQUFhUiw2Q0FBTUEsQ0FBQztJQUMxQixNQUFNLEVBQUVTLGFBQWEsRUFBRSxHQUFHTixxREFBUUE7SUFFbEMsVUFBVTtJQUNWLE1BQU1PLGNBQWNULGtEQUFXQSxDQUFDO1FBQzlCLElBQUksQ0FBQ08sV0FBV0csT0FBTyxFQUFFO1FBRXpCLElBQUk7WUFDRixJQUFJTCxrQkFBa0JLLE9BQU8sRUFBRTtnQkFDN0JMLGtCQUFrQkssT0FBTyxDQUFDQyxPQUFPO2dCQUNqQ04sa0JBQWtCSyxPQUFPLEdBQUc7WUFDOUI7UUFDRixFQUFFLE9BQU9FLE9BQVk7WUFDbkIsMEJBQTBCO1lBQzFCLElBQUlBLE9BQU9DLFNBQVMsY0FBYztnQkFDaENDLFFBQVFDLElBQUksQ0FBQyx5QkFBeUJIO1lBQ3hDO1FBQ0Y7SUFDRixHQUFHLEVBQUU7SUFFTGQsZ0RBQVNBLENBQUM7UUFDUlMsV0FBV0csT0FBTyxHQUFHO1FBRXJCLElBQUksQ0FBQ0osYUFBYUksT0FBTyxFQUFFO1FBRTNCLFVBQVU7UUFDVkQ7UUFFQSxlQUFlO1FBQ2YsTUFBTU8sWUFBWUMsV0FBVztZQUMzQixJQUFJLENBQUNWLFdBQVdHLE9BQU8sSUFBSSxDQUFDSixhQUFhSSxPQUFPLEVBQUU7WUFFbEQsSUFBSTtnQkFDRkwsa0JBQWtCSyxPQUFPLEdBQUdULG9EQUFJQSxDQUFDO29CQUMvQixHQUFHRyxLQUFLO29CQUNSYyxJQUFJWixhQUFhSSxPQUFPO29CQUN4QlMsV0FBVztvQkFDWEMsTUFBTVosa0JBQWtCO29CQUN4QmEsUUFBUTt3QkFDTkMsYUFBYTt3QkFDYkMsT0FBTzt3QkFDUEMsUUFBUTt3QkFDUkMsUUFBUTt3QkFDUkMsUUFBUTt3QkFDUkMsUUFBUTt3QkFDUkMsUUFBUTt3QkFDUkMsUUFBUTt3QkFDUkMsV0FBVzt3QkFDWEMsT0FBTzt3QkFDUEMsUUFBUTt3QkFDUkMsU0FBUzt3QkFDVEMsV0FBVzt3QkFDWEMsV0FBVzt3QkFDWEMsVUFBVTt3QkFDVkMsTUFBTTt3QkFDTkMsUUFBUTt3QkFDUkMsT0FBTzt3QkFDUEMsYUFBYTt3QkFDYkMsU0FBUzt3QkFDVEMsU0FBUzt3QkFDVEMsTUFBTTt3QkFDTkMsU0FBUzt3QkFDVEMsT0FBTzt3QkFDUEMsYUFBYTt3QkFDYkMsU0FBUzt3QkFDVEMsU0FBUzt3QkFDVEMsT0FBTzt3QkFDUEMsTUFBTTt3QkFDTkMsS0FBSztvQkFDUDtvQkFDQU4sT0FBTzt3QkFDTDt3QkFDQTt3QkFDQTt3QkFDQTt3QkFDQTt3QkFDQTt3QkFDQTt3QkFDQTtxQkFDRDtvQkFDRE8sTUFBTTt3QkFBQzt3QkFBUTt3QkFBUTtxQkFBTztvQkFDOUJDLGNBQWM7d0JBQUM7cUJBQU87b0JBQ3RCdEIsT0FBTztvQkFDUHVCLFdBQVc7d0JBQUM7d0JBQUc7cUJBQUs7b0JBQ3BCQyxVQUFVO29CQUNWQyxNQUFNO29CQUNOQyxVQUFVO29CQUNWQyxlQUFlO29CQUNmQyxhQUFhO29CQUNiQyxRQUFRO29CQUNSQyxVQUFVO2dCQUNaO1lBQ0YsRUFBRSxPQUFPakQsT0FBWTtnQkFDbkIsSUFBSUEsT0FBT0MsU0FBUyxjQUFjO29CQUNoQ0MsUUFBUUMsSUFBSSxDQUFDLHNCQUFzQkg7Z0JBQ3JDO1lBQ0Y7UUFDRixHQUFHO1FBRUgsT0FBTztZQUNMTCxXQUFXRyxPQUFPLEdBQUc7WUFDckJvRCxhQUFhOUM7WUFFYixxQkFBcUI7WUFDckJDLFdBQVc7Z0JBQ1RSO1lBQ0YsR0FBRztRQUNMO0lBQ0YsR0FBRztRQUFDRDtRQUFlQztLQUFZO0lBRS9CWCxnREFBU0EsQ0FBQztRQUNSLElBQUksQ0FBQ1MsV0FBV0csT0FBTyxFQUFFO1FBRXpCLElBQUk7WUFDRixJQUFJTCxrQkFBa0JLLE9BQU8sRUFBRTtnQkFDN0JMLGtCQUFrQkssT0FBTyxDQUFDcUQsTUFBTSxDQUFDM0Q7WUFDbkM7UUFDRixFQUFFLE9BQU9RLE9BQVk7WUFDbkIsSUFBSUEsT0FBT0MsU0FBUyxjQUFjO2dCQUNoQ0MsUUFBUUMsSUFBSSxDQUFDLHdCQUF3Qkg7WUFDdkM7UUFDRjtJQUNGLEdBQUc7UUFBQ1IsTUFBTTRELElBQUk7S0FBQztJQUVmLFVBQVU7SUFDVmxFLGdEQUFTQSxDQUFDO1FBQ1IsT0FBTztZQUNMUyxXQUFXRyxPQUFPLEdBQUc7WUFDckJEO1FBQ0Y7SUFDRixHQUFHO1FBQUNBO0tBQVk7SUFFaEIscUJBQU8sOERBQUN3RDtRQUFJQyxLQUFLNUQ7UUFBYzZELFdBQVcvRCxNQUFNK0QsU0FBUzs7Ozs7O0FBQzNEO0FBRUEsaUVBQWVoRSxhQUFhQSxFQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29yZXljaGl1LXBvcnRmb2xpby10ZW1wbGF0ZS8uL3NyYy9jb21wb25lbnRzL2NvbW1lbnQvV2FsaW5lQ29tbWVudC50c3g/OTE1NyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IFJlYWN0LCB7IHVzZUVmZmVjdCwgdXNlUmVmLCB1c2VDYWxsYmFjayB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHtcbiAgdHlwZSBXYWxpbmVJbnN0YW5jZSxcbiAgdHlwZSBXYWxpbmVJbml0T3B0aW9ucyxcbiAgaW5pdCxcbn0gZnJvbSAnQHdhbGluZS9jbGllbnQnXG5pbXBvcnQgeyB1c2VUaGVtZSB9IGZyb20gJ25leHQtdGhlbWVzJ1xuXG5pbXBvcnQgJ0B3YWxpbmUvY2xpZW50L3N0eWxlJ1xuXG5leHBvcnQgdHlwZSBXYWxpbmVPcHRpb25zID0gT21pdDxXYWxpbmVJbml0T3B0aW9ucywgJ2VsJyB8ICdzZXJ2ZXJVUkwnPiAmIHtcbiAgcGF0aDogc3RyaW5nXG4gIGNsYXNzTmFtZT86IHN0cmluZ1xufVxuXG5leHBvcnQgZnVuY3Rpb24gV2FsaW5lQ29tbWVudChwcm9wczogV2FsaW5lT3B0aW9ucykge1xuICBjb25zdCB3YWxpbmVJbnN0YW5jZVJlZiA9IHVzZVJlZjxXYWxpbmVJbnN0YW5jZSB8IG51bGw+KG51bGwpXG4gIGNvbnN0IGNvbnRhaW5lclJlZiA9IHVzZVJlZjxIVE1MRGl2RWxlbWVudD4obnVsbClcbiAgY29uc3QgbW91bnRlZFJlZiA9IHVzZVJlZih0cnVlKVxuICBjb25zdCB7IHJlc29sdmVkVGhlbWUgfSA9IHVzZVRoZW1lKClcblxuICAvLyDlronlhajnmoTplIDmr4Hlh73mlbBcbiAgY29uc3Qgc2FmZURlc3Ryb3kgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgaWYgKCFtb3VudGVkUmVmLmN1cnJlbnQpIHJldHVyblxuXG4gICAgdHJ5IHtcbiAgICAgIGlmICh3YWxpbmVJbnN0YW5jZVJlZi5jdXJyZW50KSB7XG4gICAgICAgIHdhbGluZUluc3RhbmNlUmVmLmN1cnJlbnQuZGVzdHJveSgpXG4gICAgICAgIHdhbGluZUluc3RhbmNlUmVmLmN1cnJlbnQgPSBudWxsXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgLy8g6Z2Z6buY5aSE55CGIEFib3J0RXJyb3Ig5ZKM5YW25LuW6ZSA5q+B6ZSZ6K+vXG4gICAgICBpZiAoZXJyb3I/Lm5hbWUgIT09ICdBYm9ydEVycm9yJykge1xuICAgICAgICBjb25zb2xlLndhcm4oJ1dhbGluZSBkZXN0cm95IGVycm9yOicsIGVycm9yKVxuICAgICAgfVxuICAgIH1cbiAgfSwgW10pXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBtb3VudGVkUmVmLmN1cnJlbnQgPSB0cnVlXG5cbiAgICBpZiAoIWNvbnRhaW5lclJlZi5jdXJyZW50KSByZXR1cm5cblxuICAgIC8vIOa4heeQhuS5i+WJjeeahOWunuS+i1xuICAgIHNhZmVEZXN0cm95KClcblxuICAgIC8vIOW7tui/n+WIneWni+WMluS7pemBv+WFjeernuaAgeadoeS7tlxuICAgIGNvbnN0IHRpbWVvdXRJZCA9IHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgaWYgKCFtb3VudGVkUmVmLmN1cnJlbnQgfHwgIWNvbnRhaW5lclJlZi5jdXJyZW50KSByZXR1cm5cblxuICAgICAgdHJ5IHtcbiAgICAgICAgd2FsaW5lSW5zdGFuY2VSZWYuY3VycmVudCA9IGluaXQoe1xuICAgICAgICAgIC4uLnByb3BzLFxuICAgICAgICAgIGVsOiBjb250YWluZXJSZWYuY3VycmVudCxcbiAgICAgICAgICBzZXJ2ZXJVUkw6ICdodHRwczovL3dhbGluZS5qeWFvY2hlbi5jbicsXG4gICAgICAgICAgZGFyazogcmVzb2x2ZWRUaGVtZSA9PT0gJ2RhcmsnLFxuICAgICAgICAgIGxvY2FsZToge1xuICAgICAgICAgICAgcGxhY2Vob2xkZXI6ICdTaGFyZSB5b3VyIHRob3VnaHRzIGFuZCBqb2luIHRoZSBkaXNjdXNzaW9uLi4uJyxcbiAgICAgICAgICAgIGFkbWluOiAnQWRtaW4nLFxuICAgICAgICAgICAgbGV2ZWwwOiAnTmV3Y29tZXInLFxuICAgICAgICAgICAgbGV2ZWwxOiAnRXhwbG9yZXInLFxuICAgICAgICAgICAgbGV2ZWwyOiAnQ29udHJpYnV0b3InLFxuICAgICAgICAgICAgbGV2ZWwzOiAnRXhwZXJ0JyxcbiAgICAgICAgICAgIGxldmVsNDogJ01hc3RlcicsXG4gICAgICAgICAgICBsZXZlbDU6ICdMZWdlbmQnLFxuICAgICAgICAgICAgYW5vbnltb3VzOiAnQW5vbnltb3VzJyxcbiAgICAgICAgICAgIGxvZ2luOiAnU2lnbiBJbicsXG4gICAgICAgICAgICBsb2dvdXQ6ICdTaWduIE91dCcsXG4gICAgICAgICAgICBwcm9maWxlOiAnUHJvZmlsZScsXG4gICAgICAgICAgICBuaWNrRXJyb3I6ICdOaWNrbmFtZSBtdXN0IGJlIGF0IGxlYXN0IDMgY2hhcmFjdGVycycsXG4gICAgICAgICAgICBtYWlsRXJyb3I6ICdQbGVhc2UgZW50ZXIgYSB2YWxpZCBlbWFpbCBhZGRyZXNzJyxcbiAgICAgICAgICAgIHdvcmRIaW50OiAnUGxlYXNlIGVudGVyIHlvdXIgY29tbWVudCcsXG4gICAgICAgICAgICBzb2ZhOiAnQmUgdGhlIGZpcnN0IHRvIHNoYXJlIHlvdXIgdGhvdWdodHMhJyxcbiAgICAgICAgICAgIHN1Ym1pdDogJ1B1Ymxpc2ggQ29tbWVudCcsXG4gICAgICAgICAgICByZXBseTogJ1JlcGx5JyxcbiAgICAgICAgICAgIGNhbmNlbFJlcGx5OiAnQ2FuY2VsIFJlcGx5JyxcbiAgICAgICAgICAgIGNvbW1lbnQ6ICdDb21tZW50JyxcbiAgICAgICAgICAgIHJlZnJlc2g6ICdSZWZyZXNoJyxcbiAgICAgICAgICAgIG1vcmU6ICdMb2FkIE1vcmUgQ29tbWVudHMuLi4nLFxuICAgICAgICAgICAgcHJldmlldzogJ1ByZXZpZXcnLFxuICAgICAgICAgICAgZW1vamk6ICdFbW9qaScsXG4gICAgICAgICAgICB1cGxvYWRJbWFnZTogJ1VwbG9hZCBJbWFnZScsXG4gICAgICAgICAgICBzZWNvbmRzOiAnc2Vjb25kcyBhZ28nLFxuICAgICAgICAgICAgbWludXRlczogJ21pbnV0ZXMgYWdvJyxcbiAgICAgICAgICAgIGhvdXJzOiAnaG91cnMgYWdvJyxcbiAgICAgICAgICAgIGRheXM6ICdkYXlzIGFnbycsXG4gICAgICAgICAgICBub3c6ICdqdXN0IG5vdydcbiAgICAgICAgICB9LFxuICAgICAgICAgIGVtb2ppOiBbXG4gICAgICAgICAgICAnLy91bnBrZy5jb20vQHdhbGluZS9lbW9qaXNAMS4yLjAvd2VpYm8nLFxuICAgICAgICAgICAgJy8vdW5wa2cuY29tL0B3YWxpbmUvZW1vamlzQDEuMi4wL2FsdXMnLFxuICAgICAgICAgICAgJy8vdW5wa2cuY29tL0B3YWxpbmUvZW1vamlzQDEuMi4wL2JpbGliaWxpJyxcbiAgICAgICAgICAgICcvL3VucGtnLmNvbS9Ad2FsaW5lL2Vtb2ppc0AxLjIuMC9ibW9qaScsXG4gICAgICAgICAgICAnLy91bnBrZy5jb20vQHdhbGluZS9lbW9qaXNAMS4yLjAvcXEnLFxuICAgICAgICAgICAgJy8vdW5wa2cuY29tL0B3YWxpbmUvZW1vamlzQDEuMi4wL3RpZWJhJyxcbiAgICAgICAgICAgICcvL3VucGtnLmNvbS9Ad2FsaW5lL2Vtb2ppc0AxLjIuMC90dy1lbW9qaScsXG4gICAgICAgICAgICAnLy91bnBrZy5jb20vQHdhbGluZS9lbW9qaXNAMS4yLjAvc291bC1lbW9qaScsXG4gICAgICAgICAgXSxcbiAgICAgICAgICBtZXRhOiBbJ25pY2snLCAnbWFpbCcsICdsaW5rJ10sXG4gICAgICAgICAgcmVxdWlyZWRNZXRhOiBbJ25pY2snXSxcbiAgICAgICAgICBsb2dpbjogJ2VuYWJsZScsXG4gICAgICAgICAgd29yZExpbWl0OiBbMCwgMTAwMF0sXG4gICAgICAgICAgcGFnZVNpemU6IDEwLFxuICAgICAgICAgIGxhbmc6ICdlbi1VUycsXG4gICAgICAgICAgcmVhY3Rpb246IHRydWUsXG4gICAgICAgICAgaW1hZ2VVcGxvYWRlcjogZmFsc2UsXG4gICAgICAgICAgdGV4UmVuZGVyZXI6IGZhbHNlLFxuICAgICAgICAgIHNlYXJjaDogZmFsc2UsXG4gICAgICAgICAgcGFnZXZpZXc6IHRydWVcbiAgICAgICAgfSlcbiAgICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgICAgaWYgKGVycm9yPy5uYW1lICE9PSAnQWJvcnRFcnJvcicpIHtcbiAgICAgICAgICBjb25zb2xlLndhcm4oJ1dhbGluZSBpbml0IGVycm9yOicsIGVycm9yKVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfSwgMTAwKVxuXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIG1vdW50ZWRSZWYuY3VycmVudCA9IGZhbHNlXG4gICAgICBjbGVhclRpbWVvdXQodGltZW91dElkKVxuXG4gICAgICAvLyDlu7bov5/plIDmr4Hku6Xpgb/lhY0gQWJvcnRFcnJvclxuICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIHNhZmVEZXN0cm95KClcbiAgICAgIH0sIDUwKVxuICAgIH1cbiAgfSwgW3Jlc29sdmVkVGhlbWUsIHNhZmVEZXN0cm95XSlcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghbW91bnRlZFJlZi5jdXJyZW50KSByZXR1cm5cblxuICAgIHRyeSB7XG4gICAgICBpZiAod2FsaW5lSW5zdGFuY2VSZWYuY3VycmVudCkge1xuICAgICAgICB3YWxpbmVJbnN0YW5jZVJlZi5jdXJyZW50LnVwZGF0ZShwcm9wcylcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBpZiAoZXJyb3I/Lm5hbWUgIT09ICdBYm9ydEVycm9yJykge1xuICAgICAgICBjb25zb2xlLndhcm4oJ1dhbGluZSB1cGRhdGUgZXJyb3I6JywgZXJyb3IpXG4gICAgICB9XG4gICAgfVxuICB9LCBbcHJvcHMucGF0aF0pXG5cbiAgLy8g57uE5Lu25Y246L295pe25riF55CGXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIG1vdW50ZWRSZWYuY3VycmVudCA9IGZhbHNlXG4gICAgICBzYWZlRGVzdHJveSgpXG4gICAgfVxuICB9LCBbc2FmZURlc3Ryb3ldKVxuXG4gIHJldHVybiA8ZGl2IHJlZj17Y29udGFpbmVyUmVmfSBjbGFzc05hbWU9e3Byb3BzLmNsYXNzTmFtZX0gLz5cbn1cblxuZXhwb3J0IGRlZmF1bHQgV2FsaW5lQ29tbWVudFxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlRWZmZWN0IiwidXNlUmVmIiwidXNlQ2FsbGJhY2siLCJpbml0IiwidXNlVGhlbWUiLCJXYWxpbmVDb21tZW50IiwicHJvcHMiLCJ3YWxpbmVJbnN0YW5jZVJlZiIsImNvbnRhaW5lclJlZiIsIm1vdW50ZWRSZWYiLCJyZXNvbHZlZFRoZW1lIiwic2FmZURlc3Ryb3kiLCJjdXJyZW50IiwiZGVzdHJveSIsImVycm9yIiwibmFtZSIsImNvbnNvbGUiLCJ3YXJuIiwidGltZW91dElkIiwic2V0VGltZW91dCIsImVsIiwic2VydmVyVVJMIiwiZGFyayIsImxvY2FsZSIsInBsYWNlaG9sZGVyIiwiYWRtaW4iLCJsZXZlbDAiLCJsZXZlbDEiLCJsZXZlbDIiLCJsZXZlbDMiLCJsZXZlbDQiLCJsZXZlbDUiLCJhbm9ueW1vdXMiLCJsb2dpbiIsImxvZ291dCIsInByb2ZpbGUiLCJuaWNrRXJyb3IiLCJtYWlsRXJyb3IiLCJ3b3JkSGludCIsInNvZmEiLCJzdWJtaXQiLCJyZXBseSIsImNhbmNlbFJlcGx5IiwiY29tbWVudCIsInJlZnJlc2giLCJtb3JlIiwicHJldmlldyIsImVtb2ppIiwidXBsb2FkSW1hZ2UiLCJzZWNvbmRzIiwibWludXRlcyIsImhvdXJzIiwiZGF5cyIsIm5vdyIsIm1ldGEiLCJyZXF1aXJlZE1ldGEiLCJ3b3JkTGltaXQiLCJwYWdlU2l6ZSIsImxhbmciLCJyZWFjdGlvbiIsImltYWdlVXBsb2FkZXIiLCJ0ZXhSZW5kZXJlciIsInNlYXJjaCIsInBhZ2V2aWV3IiwiY2xlYXJUaW1lb3V0IiwidXBkYXRlIiwicGF0aCIsImRpdiIsInJlZiIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/comment/WalineComment.tsx\n");

/***/ })

};
;