"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/marked-highlight";
exports.ids = ["vendor-chunks/marked-highlight"];
exports.modules = {

/***/ "(ssr)/./node_modules/marked-highlight/src/index.js":
/*!****************************************************!*\
  !*** ./node_modules/marked-highlight/src/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   markedHighlight: () => (/* binding */ markedHighlight)\n/* harmony export */ });\nfunction markedHighlight(options) {\n  if (typeof options === 'function') {\n    options = {\n      highlight: options,\n    };\n  }\n\n  if (!options || typeof options.highlight !== 'function') {\n    throw new Error('Must provide highlight function');\n  }\n\n  if (typeof options.langPrefix !== 'string') {\n    options.langPrefix = 'language-';\n  }\n\n  if (typeof options.emptyLangClass !== 'string') {\n    options.emptyLangClass = '';\n  }\n\n  return {\n    async: !!options.async,\n    walkTokens(token) {\n      if (token.type !== 'code') {\n        return;\n      }\n\n      const lang = getLang(token.lang);\n\n      if (options.async) {\n        return Promise.resolve(options.highlight(token.text, lang, token.lang || '')).then(updateToken(token));\n      }\n\n      const code = options.highlight(token.text, lang, token.lang || '');\n      if (code instanceof Promise) {\n        throw new Error('markedHighlight is not set to async but the highlight function is async. Set the async option to true on markedHighlight to await the async highlight function.');\n      }\n      updateToken(token)(code);\n    },\n    useNewRenderer: true,\n    renderer: {\n      code(code, infoString, escaped) {\n        // istanbul ignore next\n        if (typeof code === 'object') {\n          escaped = code.escaped;\n          infoString = code.lang;\n          code = code.text;\n        }\n        const lang = getLang(infoString);\n        const classValue = lang ? options.langPrefix + escape(lang) : options.emptyLangClass;\n        const classAttr = classValue\n          ? ` class=\"${classValue}\"`\n          : '';\n        code = code.replace(/\\n$/, '');\n        return `<pre><code${classAttr}>${escaped ? code : escape(code, true)}\\n</code></pre>`;\n      },\n    },\n  };\n}\n\nfunction getLang(lang) {\n  return (lang || '').match(/\\S*/)[0];\n}\n\nfunction updateToken(token) {\n  return (code) => {\n    if (typeof code === 'string' && code !== token.text) {\n      token.escaped = true;\n      token.text = code;\n    }\n  };\n}\n\n// copied from marked helpers\nconst escapeTest = /[&<>\"']/;\nconst escapeReplace = new RegExp(escapeTest.source, 'g');\nconst escapeTestNoEncode = /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/;\nconst escapeReplaceNoEncode = new RegExp(escapeTestNoEncode.source, 'g');\nconst escapeReplacements = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#39;',\n};\nconst getEscapeReplacement = (ch) => escapeReplacements[ch];\nfunction escape(html, encode) {\n  if (encode) {\n    if (escapeTest.test(html)) {\n      return html.replace(escapeReplace, getEscapeReplacement);\n    }\n  } else {\n    if (escapeTestNoEncode.test(html)) {\n      return html.replace(escapeReplaceNoEncode, getEscapeReplacement);\n    }\n  }\n\n  return html;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/marked-highlight/src/index.js\n");

/***/ })

};
;