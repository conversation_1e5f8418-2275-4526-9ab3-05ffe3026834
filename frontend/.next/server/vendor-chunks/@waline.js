"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@waline";
exports.ids = ["vendor-chunks/@waline"];
exports.modules = {

/***/ "(ssr)/./node_modules/@waline/client/dist/waline.css":
/*!*****************************************************!*\
  !*** ./node_modules/@waline/client/dist/waline.css ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"09cabe31b93c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhbGluZS9jbGllbnQvZGlzdC93YWxpbmUuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29yZXljaGl1LXBvcnRmb2xpby10ZW1wbGF0ZS8uL25vZGVfbW9kdWxlcy9Ad2FsaW5lL2NsaWVudC9kaXN0L3dhbGluZS5jc3M/Nzk3OSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjA5Y2FiZTMxYjkzY1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@waline/client/dist/waline.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/@waline/api/dist/api.js":
/*!**********************************************!*\
  !*** ./node_modules/@waline/api/dist/api.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addComment: () => (/* binding */ u),\n/* harmony export */   deleteComment: () => (/* binding */ y),\n/* harmony export */   fetchCommentCount: () => (/* binding */ f),\n/* harmony export */   getArticleCounter: () => (/* binding */ p),\n/* harmony export */   getComment: () => (/* binding */ $),\n/* harmony export */   getPageview: () => (/* binding */ j),\n/* harmony export */   getRecentComment: () => (/* binding */ w),\n/* harmony export */   getUserList: () => (/* binding */ C),\n/* harmony export */   login: () => (/* binding */ R),\n/* harmony export */   updateArticleCounter: () => (/* binding */ d),\n/* harmony export */   updateComment: () => (/* binding */ U),\n/* harmony export */   updatePageview: () => (/* binding */ v)\n/* harmony export */ });\nconst m={\"Content-Type\":\"application/json\"},s=e=>`${e.replace(/\\/?$/,\"/\")}api/`,c=(e,n=\"\")=>{if(typeof e==\"object\"&&e.errno)throw new TypeError(`${n} failed with ${e.errno}: ${e.errmsg}`);return e},p=({serverURL:e,lang:n,paths:o,type:a,signal:t})=>fetch(`${s(e)}article?path=${encodeURIComponent(o.join(\",\"))}&type=${encodeURIComponent(a.join(\",\"))}&lang=${n}`,{signal:t}).then(r=>r.json()).then(r=>c(r,\"Get counter\").data),d=({serverURL:e,lang:n,path:o,type:a,action:t})=>fetch(`${s(e)}article?lang=${n}`,{method:\"POST\",headers:m,body:JSON.stringify({path:o,type:a,action:t})}).then(r=>r.json()).then(r=>c(r,\"Update counter\").data),$=({serverURL:e,lang:n,path:o,page:a,pageSize:t,sortBy:r,signal:h,token:i})=>{const l={};return i&&(l.Authorization=`Bearer ${i}`),fetch(`${s(e)}comment?path=${encodeURIComponent(o)}&pageSize=${t}&page=${a}&lang=${n}&sortBy=${r}`,{signal:h,headers:l}).then(g=>g.json()).then(g=>c(g,\"Get comment data\").data)},u=({serverURL:e,lang:n,token:o,comment:a})=>{const t={\"Content-Type\":\"application/json\"};return o&&(t.Authorization=`Bearer ${o}`),fetch(`${s(e)}comment?lang=${n}`,{method:\"POST\",headers:t,body:JSON.stringify(a)}).then(r=>r.json())},y=({serverURL:e,lang:n,token:o,objectId:a})=>fetch(`${s(e)}comment/${a}?lang=${n}`,{method:\"DELETE\",headers:{Authorization:`Bearer ${o}`}}).then(t=>t.json()).then(t=>c(t,\"Delete comment\")),U=({serverURL:e,lang:n,token:o,objectId:a,comment:t})=>fetch(`${s(e)}comment/${a}?lang=${n}`,{method:\"PUT\",headers:{...m,Authorization:`Bearer ${o}`},body:JSON.stringify(t)}).then(r=>r.json()).then(r=>c(r,\"Update comment\")),f=({serverURL:e,lang:n,paths:o,signal:a})=>fetch(`${s(e)}comment?type=count&url=${encodeURIComponent(o.join(\",\"))}&lang=${n}`,{signal:a}).then(t=>t.json()).then(t=>c(t,\"Get comment count\").data),R=({lang:e,serverURL:n})=>{const o=(window.innerWidth-450)/2,a=(window.innerHeight-450)/2,t=window.open(`${n.replace(/\\/$/,\"\")}/ui/login?lng=${encodeURIComponent(e)}`,\"_blank\",`width=450,height=450,left=${o},top=${a},scrollbars=no,resizable=no,status=no,location=no,toolbar=no,menubar=no`);return t?.postMessage({type:\"TOKEN\",data:null},\"*\"),new Promise(r=>{const h=({data:i})=>{!i||typeof i!=\"object\"||i.type!==\"userInfo\"||i.data.token&&(t?.close(),window.removeEventListener(\"message\",h),r(i.data))};window.addEventListener(\"message\",h)})},j=({serverURL:e,lang:n,paths:o,signal:a})=>p({serverURL:e,lang:n,paths:o,type:[\"time\"],signal:a}),v=e=>d({...e,type:\"time\",action:\"inc\"}),w=({serverURL:e,lang:n,count:o,signal:a,token:t})=>{const r={};return t&&(r.Authorization=`Bearer ${t}`),fetch(`${s(e)}comment?type=recent&count=${o}&lang=${n}`,{signal:a,headers:r}).then(h=>h.json())},C=({serverURL:e,signal:n,pageSize:o,lang:a})=>fetch(`${s(e)}user?pageSize=${o}&lang=${a}`,{signal:n}).then(t=>t.json()).then(t=>c(t,\"user list\")).then(t=>t.data);\n//# sourceMappingURL=api.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@waline/api/dist/api.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@waline/client/dist/pageview.js":
/*!******************************************************!*\
  !*** ./node_modules/@waline/client/dist/pageview.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pageviewCount: () => (/* binding */ S),\n/* harmony export */   version: () => (/* binding */ v)\n/* harmony export */ });\nconst v=\"3.6.0\",$={\"Content-Type\":\"application/json\"},h=e=>`${e.replace(/\\/?$/,\"/\")}api/`,u=(e,t=\"\")=>{if(typeof e==\"object\"&&e.errno)throw new TypeError(`${t} failed with ${e.errno}: ${e.errmsg}`);return e},f=({serverURL:e,lang:t,paths:r,type:o,signal:a})=>fetch(`${h(e)}article?path=${encodeURIComponent(r.join(\",\"))}&type=${encodeURIComponent(o.join(\",\"))}&lang=${t}`,{signal:a}).then(n=>n.json()).then(n=>u(n,\"Get counter\").data),R=({serverURL:e,lang:t,path:r,type:o,action:a})=>fetch(`${h(e)}article?lang=${t}`,{method:\"POST\",headers:$,body:JSON.stringify({path:r,type:o,action:a})}).then(n=>n.json()).then(n=>u(n,\"Update counter\").data),U=({serverURL:e,lang:t,paths:r,signal:o})=>f({serverURL:e,lang:t,paths:r,type:[\"time\"],signal:o}),w=e=>R({...e,type:\"time\",action:\"inc\"}),L=(e=\"\")=>e.replace(/\\/$/u,\"\"),b=e=>/^(https?:)?\\/\\//.test(e),d=e=>{const t=L(e);return b(t)?t:`https://${t}`},j=e=>{e.name!==\"AbortError\"&&console.error(e.message)},m=e=>{const{path:t}=e.dataset;return t!=null&&t.length?t:null},y=(e,t)=>{t.forEach((r,o)=>{const a=e[o].time;typeof a==\"number\"&&(r.innerText=a.toString())})},S=({serverURL:e,path:t=window.location.pathname,selector:r=\".waline-pageview-count\",update:o=!0,lang:a=navigator.language})=>{const n=new AbortController,i=Array.from(document.querySelectorAll(r)),p=l=>{const s=m(l);return s!==null&&t!==s},g=l=>U({serverURL:d(e),paths:l.map(s=>m(s)??t),lang:a,signal:n.signal}).then(s=>y(s,l)).catch(j);if(o){const l=i.filter(c=>!p(c)),s=i.filter(p);w({serverURL:d(e),path:t,lang:a}).then(c=>y(c,l)),s.length&&g(s)}else g(i);return n.abort.bind(n)};\n//# sourceMappingURL=pageview.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@waline/client/dist/pageview.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@waline/client/dist/slim.js":
/*!**************************************************!*\
  !*** ./node_modules/@waline/client/dist/slim.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecentComments: () => (/* binding */ Un),\n/* harmony export */   UserList: () => (/* binding */ zn),\n/* harmony export */   addComment: () => (/* reexport safe */ _waline_api__WEBPACK_IMPORTED_MODULE_0__.addComment),\n/* harmony export */   commentCount: () => (/* binding */ mt),\n/* harmony export */   defaultLocales: () => (/* binding */ fe),\n/* harmony export */   deleteComment: () => (/* reexport safe */ _waline_api__WEBPACK_IMPORTED_MODULE_0__.deleteComment),\n/* harmony export */   fetchCommentCount: () => (/* reexport safe */ _waline_api__WEBPACK_IMPORTED_MODULE_0__.fetchCommentCount),\n/* harmony export */   getArticleCounter: () => (/* reexport safe */ _waline_api__WEBPACK_IMPORTED_MODULE_0__.getArticleCounter),\n/* harmony export */   getComment: () => (/* reexport safe */ _waline_api__WEBPACK_IMPORTED_MODULE_0__.getComment),\n/* harmony export */   getPageview: () => (/* reexport safe */ _waline_api__WEBPACK_IMPORTED_MODULE_0__.getPageview),\n/* harmony export */   getRecentComment: () => (/* reexport safe */ _waline_api__WEBPACK_IMPORTED_MODULE_0__.getRecentComment),\n/* harmony export */   getUserList: () => (/* reexport safe */ _waline_api__WEBPACK_IMPORTED_MODULE_0__.getUserList),\n/* harmony export */   init: () => (/* binding */ jn),\n/* harmony export */   login: () => (/* reexport safe */ _waline_api__WEBPACK_IMPORTED_MODULE_0__.login),\n/* harmony export */   pageviewCount: () => (/* binding */ wt),\n/* harmony export */   updateArticleCounter: () => (/* reexport safe */ _waline_api__WEBPACK_IMPORTED_MODULE_0__.updateArticleCounter),\n/* harmony export */   updateComment: () => (/* reexport safe */ _waline_api__WEBPACK_IMPORTED_MODULE_0__.updateComment),\n/* harmony export */   updatePageview: () => (/* reexport safe */ _waline_api__WEBPACK_IMPORTED_MODULE_0__.updatePageview),\n/* harmony export */   version: () => (/* binding */ ht)\n/* harmony export */ });\n/* harmony import */ var _waline_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @waline/api */ \"(ssr)/./node_modules/@waline/api/dist/api.js\");\n/* harmony import */ var vue__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! vue */ \"(ssr)/./node_modules/vue/index.mjs\");\n/* harmony import */ var _vueuse_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @vueuse/core */ \"(ssr)/./node_modules/@vueuse/core/index.mjs\");\n/* harmony import */ var _vueuse_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @vueuse/core */ \"(ssr)/./node_modules/@vueuse/shared/index.mjs\");\n/* harmony import */ var autosize__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! autosize */ \"(ssr)/./node_modules/autosize/dist/autosize.esm.js\");\n/* harmony import */ var marked__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! marked */ \"(ssr)/./node_modules/marked/lib/marked.esm.js\");\n/* harmony import */ var marked_highlight__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! marked-highlight */ \"(ssr)/./node_modules/marked-highlight/src/index.js\");\n/* harmony import */ var recaptcha_v3__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! recaptcha-v3 */ \"(ssr)/./node_modules/recaptcha-v3/dist/ReCaptcha.js\");\nconst Kt=[\"nick\",\"mail\",\"link\"],De=e=>e.filter(l=>Kt.includes(l)),Pe=[\"//unpkg.com/@waline/emojis@1.1.0/weibo\"],Zt=[\"//unpkg.com/@waline/emojis/tieba/tieba_agree.png\",\"//unpkg.com/@waline/emojis/tieba/tieba_look_down.png\",\"//unpkg.com/@waline/emojis/tieba/tieba_sunglasses.png\",\"//unpkg.com/@waline/emojis/tieba/tieba_pick_nose.png\",\"//unpkg.com/@waline/emojis/tieba/tieba_awkward.png\",\"//unpkg.com/@waline/emojis/tieba/tieba_sleep.png\"],Xt=e=>new Promise((l,t)=>{if(e.size>128e3)return t(new Error(\"File too large! File size limit 128KB\"));const a=new FileReader;a.readAsDataURL(e),a.onload=()=>l(a.result),a.onerror=t}),Yt=e=>e?'<p class=\"wl-tex\">TeX is not available in preview</p>':'<span class=\"wl-tex\">TeX is not available in preview</span>',Jt=e=>{const l=async(t,a={})=>fetch(`https://api.giphy.com/v1/gifs/${t}?${new URLSearchParams({lang:e,limit:\"20\",rating:\"g\",api_key:\"6CIMLkNMMOhRcXPoMCPkFy4Ybk2XUiMp\",...a}).toString()}`).then(n=>n.json()).then(({data:n})=>n.map(s=>({title:s.title,src:s.images.downsized_medium.url})));return{search:t=>l(\"search\",{q:t,offset:\"0\"}),default:()=>l(\"trending\",{}),more:(t,a=0)=>l(\"search\",{q:t,offset:a.toString()})}},Qt=/[\\u4E00-\\u9FFF\\u3400-\\u4dbf\\uf900-\\ufaff\\u3040-\\u309f\\uac00-\\ud7af\\u0400-\\u04FF]+|\\w+/,ea=/</,ta=/(?:^|\\s)\\/\\/(.+?)$/gm,aa=/\\/\\*([\\S\\s]*?)\\*\\//gm,la=new RegExp(`(${Qt.source}|${ea.source})|((?:${ta.source})|(?:${aa.source}))`,\"gmi\"),qe=[\"23AC69\",\"91C132\",\"F19726\",\"E8552D\",\"1AAB8E\",\"E1147F\",\"2980C1\",\"1BA1E6\",\"9FA0A0\",\"F19726\",\"E30B20\",\"E30B20\",\"A3338B\"],Ie={},na=e=>{let l=0;return e.replace(la,(t,a,n)=>{if(n)return`<span style=\"color: slategray\">${n}</span>`;if(a===\"<\")return\"&lt;\";let s;Ie[a]?s=Ie[a]:(s=qe[l],Ie[a]=s);const h=`<span style=\"color: #${s}\">${a}</span>`;return l=++l%qe.length,h})},ra=[\"nick\",\"nickError\",\"mail\",\"mailError\",\"link\",\"optional\",\"placeholder\",\"sofa\",\"submit\",\"like\",\"cancelLike\",\"reply\",\"cancelReply\",\"comment\",\"refresh\",\"more\",\"preview\",\"emoji\",\"uploadImage\",\"seconds\",\"minutes\",\"hours\",\"days\",\"now\",\"uploading\",\"login\",\"logout\",\"admin\",\"sticky\",\"word\",\"wordHint\",\"anonymous\",\"level0\",\"level1\",\"level2\",\"level3\",\"level4\",\"level5\",\"gif\",\"gifSearchPlaceholder\",\"profile\",\"approved\",\"waiting\",\"spam\",\"unsticky\",\"oldest\",\"latest\",\"hottest\",\"reactionTitle\"],J=e=>Object.fromEntries(e.map((l,t)=>[ra[t],l]));var oa=J([\"Benutzername\",\"Der Benutzername darf nicht weniger als 3 Bytes umfassen.\",\"E-Mail\",\"Bitte bestätigen Sie Ihre E-Mail-Adresse.\",\"Webseite\",\"Optional\",\"Kommentieren Sie hier...\",\"Noch keine Kommentare.\",\"Senden\",\"Gefällt mir\",\"Gefällt mir nicht mehr\",\"Antworten\",\"Antwort abbrechen\",\"Kommentare\",\"Aktualisieren\",\"Mehr laden...\",\"Vorschau\",\"Emoji\",\"Ein Bild hochladen\",\"Vor einigen Sekunden\",\"Vor einigen Minuten\",\"Vor einigen Stunden\",\"Vor einigen Tagen\",\"Gerade eben\",\"Hochladen läuft\",\"Anmelden\",\"Abmelden\",\"Admin\",\"Angeheftet\",\"Wörter\",\"Bitte geben Sie Kommentare zwischen $0 und $1 Wörtern ein! Aktuelle Anzahl der Wörter: $2\",\"Anonym\",\"Zwerge\",\"Hobbits\",\"Ents\",\"Magier\",\"Elfen\",\"Maïar\",\"GIF\",\"Nach einem GIF suchen\",\"Profil\",\"Genehmigt\",\"Ausstehend\",\"Spam\",\"Lösen\",\"Älteste\",\"Neueste\",\"Am beliebtesten\",\"Was denken Sie?\"]),Oe=J([\"NickName\",\"NickName cannot be less than 3 bytes.\",\"E-Mail\",\"Please confirm your email address.\",\"Website\",\"Optional\",\"Comment here...\",\"No comment yet.\",\"Submit\",\"Like\",\"Cancel like\",\"Reply\",\"Cancel reply\",\"Comments\",\"Refresh\",\"Load More...\",\"Preview\",\"Emoji\",\"Upload Image\",\"seconds ago\",\"minutes ago\",\"hours ago\",\"days ago\",\"just now\",\"Uploading\",\"Login\",\"logout\",\"Admin\",\"Sticky\",\"Words\",`Please input comments between $0 and $1 words!\n Current word number: $2`,\"Anonymous\",\"Dwarves\",\"Hobbits\",\"Ents\",\"Wizards\",\"Elves\",\"Maiar\",\"GIF\",\"Search GIF\",\"Profile\",\"Approved\",\"Waiting\",\"Spam\",\"Unsticky\",\"Oldest\",\"Latest\",\"Hottest\",\"What do you think?\"]),Ge=J([\"Nombre de usuario\",\"El nombre de usuario no puede tener menos de 3 bytes.\",\"Correo electrónico\",\"Por favor confirma tu dirección de correo electrónico.\",\"Sitio web\",\"Opcional\",\"Comenta aquí...\",\"Sin comentarios todavía.\",\"Enviar\",\"Like\",\"Anular like\",\"Responder\",\"Anular respuesta\",\"Comentarios\",\"Recargar\",\"Cargar Más...\",\"Previsualizar\",\"Emoji\",\"Subir Imagen\",\"segundos atrás\",\"minutos atrás\",\"horas atrás\",\"días atrás\",\"justo ahora\",\"Subiendo\",\"Iniciar sesión\",\"cerrar sesión\",\"Admin\",\"Fijado\",\"Palabras\",`Por favor escriba entre $0 y $1 palabras!\n El número actual de palabras: $2`,\"Anónimo\",\"Enanos\",\"Hobbits\",\"Ents\",\"Magos\",\"Elfos\",\"Maiar\",\"GIF\",\"Buscar GIF\",\"Perfil\",\"Aprobado\",\"Esperando\",\"Spam\",\"Desfijar\",\"Más antiguos\",\"Más recientes\",\"Más vistos\",\"¿Qué piensas?\"]),Ke=J([\"Pseudo\",\"Le pseudo ne peut pas faire moins de 3 octets.\",\"E-mail\",\"Veuillez confirmer votre adresse e-mail.\",\"Site Web\",\"Optionnel\",\"Commentez ici...\",\"Aucun commentaire pour l'instant.\",\"Envoyer\",\"J'aime\",\"Annuler le j'aime\",\"Répondre\",\"Annuler la réponse\",\"Commentaires\",\"Actualiser\",\"Charger plus...\",\"Aperçu\",\"Emoji\",\"Télécharger une image\",\"Il y a quelques secondes\",\"Il y a quelques minutes\",\"Il y a quelques heures\",\"Il y a quelques jours\",\"À l'instant\",\"Téléchargement en cours\",\"Connexion\",\"Déconnexion\",\"Admin\",\"Épinglé\",\"Mots\",`Veuillez saisir des commentaires entre $0 et $1 mots !\n Nombre actuel de mots : $2`,\"Anonyme\",\"Nains\",\"Hobbits\",\"Ents\",\"Mages\",\"Elfes\",\"Maïar\",\"GIF\",\"Rechercher un GIF\",\"Profil\",\"Approuvé\",\"En attente\",\"Indésirable\",\"Détacher\",\"Le plus ancien\",\"Dernier\",\"Le plus populaire\",\"Qu'en pensez-vous ?\"]),Ze=J([\"ニックネーム\",\"3バイト以上のニックネームをご入力ください.\",\"メールアドレス\",\"メールアドレスをご確認ください.\",\"サイト\",\"オプション\",\"ここにコメント\",\"コメントしましょう~\",\"提出する\",\"Like\",\"Cancel like\",\"返信する\",\"キャンセル\",\"コメント\",\"更新\",\"さらに読み込む\",\"プレビュー\",\"絵文字\",\"画像をアップロード\",\"秒前\",\"分前\",\"時間前\",\"日前\",\"たっだ今\",\"アップロード\",\"ログインする\",\"ログアウト\",\"管理者\",\"トップに置く\",\"ワード\",`コメントは $0 から $1 ワードの間でなければなりません!\n 現在の単語番号: $2`,\"匿名\",\"うえにん\",\"なかにん\",\"しもおし\",\"特にしもおし\",\"かげ\",\"なぬし\",\"GIF\",\"探す GIF\",\"個人情報\",\"承認済み\",\"待っている\",\"スパム\",\"べたつかない\",\"逆順\",\"正順\",\"人気順\",\"どう思いますか？\"]),ia=J([\"Apelido\",\"Apelido não pode ser menor que 3 bytes.\",\"E-Mail\",\"Por favor, confirme seu endereço de e-mail.\",\"Website\",\"Opcional\",\"Comente aqui...\",\"Nenhum comentário, ainda.\",\"Enviar\",\"Like\",\"Cancel like\",\"Responder\",\"Cancelar resposta\",\"Comentários\",\"Refrescar\",\"Carregar Mais...\",\"Visualizar\",\"Emoji\",\"Enviar Imagem\",\"segundos atrás\",\"minutos atrás\",\"horas atrás\",\"dias atrás\",\"agora mesmo\",\"Enviando\",\"Entrar\",\"Sair\",\"Admin\",\"Sticky\",\"Palavras\",`Favor enviar comentário com $0 a $1 palavras!\n Número de palavras atuais: $2`,\"Anônimo\",\"Dwarves\",\"Hobbits\",\"Ents\",\"Wizards\",\"Elves\",\"Maiar\",\"GIF\",\"Pesquisar GIF\",\"informação pessoal\",\"Aprovado\",\"Espera\",\"Spam\",\"Unsticky\",\"Mais velho\",\"Mais recentes\",\"Mais quente\",\"O que você acha?\"]),Xe=J([\"Псевдоним\",\"Никнейм не может быть меньше 3 байт.\",\"Эл. адрес\",\"Пожалуйста, подтвердите адрес вашей электронной почты.\",\"Веб-сайт\",\"Необязательный\",\"Комментарий здесь...\",\"Пока нет комментариев.\",\"Отправить\",\"Like\",\"Cancel like\",\"Отвечать\",\"Отменить ответ\",\"Комментарии\",\"Обновить\",\"Загрузи больше...\",\"Превью\",\"эмодзи\",\"Загрузить изображение\",\"секунд назад\",\"несколько минут назад\",\"несколько часов назад\",\"дней назад\",\"прямо сейчас\",\"Загрузка\",\"Авторизоваться\",\"Выход из системы\",\"Админ\",\"Липкий\",\"Слова\",`Пожалуйста, введите комментарии от $0 до $1 слов!\nНомер текущего слова: $2`,\"Анонимный\",\"Dwarves\",\"Hobbits\",\"Ents\",\"Wizards\",\"Elves\",\"Maiar\",\"GIF\",\"Поиск GIF\",\"Персональные данные\",\"Одобренный\",\"Ожидающий\",\"Спам\",\"Нелипкий\",\"самый старый\",\"последний\",\"самый горячий\",\"Что вы думаете?\"]),Ye=J([\"Tên\",\"Tên không được nhỏ hơn 3 ký tự.\",\"E-Mail\",\"Vui lòng xác nhập địa chỉ email của bạn.\",\"Website\",\"Tùy chọn\",\"Hãy bình luận có văn hoá!\",\"Chưa có bình luận\",\"Gửi\",\"Thích\",\"Bỏ thích\",\"Trả lời\",\"Hủy bỏ\",\"bình luận\",\"Làm mới\",\"Tải thêm...\",\"Xem trước\",\"Emoji\",\"Tải lên hình ảnh\",\"giây trước\",\"phút trước\",\"giờ trước\",\"ngày trước\",\"Vừa xong\",\"Đang tải lên\",\"Đăng nhập\",\"đăng xuất\",\"Quản trị viên\",\"Dính\",\"từ\",`Bình luận phải có độ dài giữa $0 và $1 từ!\n Số từ hiện tại: $2`,\"Vô danh\",\"Người lùn\",\"Người tí hon\",\"Thần rừng\",\"Pháp sư\",\"Tiên tộc\",\"Maiar\",\"Ảnh GIF\",\"Tìm kiếm ảnh GIF\",\"thông tin cá nhân\",\"Đã được phê duyệt\",\"Đang chờ đợi\",\"Thư rác\",\"Không dính\",\"lâu đời nhất\",\"muộn nhất\",\"nóng nhất\",\"What do you think?\"]),Je=J([\"昵称\",\"昵称不能少于3个字符\",\"邮箱\",\"请填写正确的邮件地址\",\"网址\",\"可选\",\"欢迎评论\",\"来发评论吧~\",\"提交\",\"喜欢\",\"取消喜欢\",\"回复\",\"取消回复\",\"评论\",\"刷新\",\"加载更多...\",\"预览\",\"表情\",\"上传图片\",\"秒前\",\"分钟前\",\"小时前\",\"天前\",\"刚刚\",\"正在上传\",\"登录\",\"退出\",\"博主\",\"置顶\",\"字\",`评论字数应在 $0 到 $1 字之间！\n当前字数：$2`,\"匿名\",\"潜水\",\"冒泡\",\"吐槽\",\"活跃\",\"话痨\",\"传说\",\"表情包\",\"搜索表情包\",\"个人资料\",\"通过\",\"待审核\",\"垃圾\",\"取消置顶\",\"按倒序\",\"按正序\",\"按热度\",\"你认为这篇文章怎么样？\"]),sa=J([\"暱稱\",\"暱稱不能少於3個字元\",\"郵箱\",\"請填寫正確的郵件地址\",\"網址\",\"可選\",\"歡迎留言\",\"來發留言吧~\",\"送出\",\"喜歡\",\"取消喜歡\",\"回覆\",\"取消回覆\",\"留言\",\"重整\",\"載入更多...\",\"預覽\",\"表情\",\"上傳圖片\",\"秒前\",\"分鐘前\",\"小時前\",\"天前\",\"剛剛\",\"正在上傳\",\"登入\",\"登出\",\"管理者\",\"置頂\",\"字\",`留言字數應在 $0 到 $1 字之間！\n目前字數：$2`,\"匿名\",\"潛水\",\"冒泡\",\"吐槽\",\"活躍\",\"多話\",\"傳說\",\"表情包\",\"搜尋表情包\",\"個人資料\",\"通過\",\"待審核\",\"垃圾\",\"取消置頂\",\"最早\",\"最新\",\"熱門\",\"你認為這篇文章怎麼樣？\"]);const Qe=\"en-US\",fe={zh:Je,\"zh-cn\":Je,\"zh-tw\":sa,en:Oe,\"en-us\":Oe,fr:Ke,\"fr-fr\":Ke,jp:Ze,\"jp-jp\":Ze,\"pt-br\":ia,ru:Xe,\"ru-ru\":Xe,vi:Ye,\"vi-vn\":Ye,de:oa,es:Ge,\"es-mx\":Ge},et=e=>fe[e.toLowerCase()]||fe[Qe.toLowerCase()],tt=e=>Object.keys(fe).includes(e.toLowerCase())?e:Qe,at={latest:\"insertedAt_desc\",oldest:\"insertedAt_asc\",hottest:\"like_desc\"},ca=Object.keys(at),we=Symbol(\"waline-config\"),lt=e=>{try{e=decodeURI(e)}catch{}return e},nt=(e=\"\")=>e.replace(/\\/$/u,\"\"),rt=e=>/^(https?:)?\\/\\//.test(e),ye=e=>{const l=nt(e);return rt(l)?l:`https://${l}`},ua=e=>Array.isArray(e)?e:e?[0,e]:!1,ce=(e,l)=>e==null||e===!0?l:e===!1?null:e,ma=({serverURL:e,path:l=location.pathname,lang:t=typeof navigator>\"u\"?\"en-US\":navigator.language,locale:a,meta:n=[\"nick\",\"mail\",\"link\"],requiredMeta:s=[],dark:h=!1,pageSize:c=10,wordLimit:f,noCopyright:v=!1,login:y=\"enable\",recaptchaV3Key:m=\"\",turnstileKey:R=\"\",commentSorting:j=\"latest\",emoji:E=Pe,imageUploader:$,highlighter:S,texRenderer:i,search:g,reaction:K,...X})=>({serverURL:ye(e),path:lt(l),lang:tt(t),locale:{...et(tt(t)),...typeof a==\"object\"?a:{}},wordLimit:ua(f),meta:De(n),requiredMeta:De(s),dark:h,pageSize:c,commentSorting:j,login:y,noCopyright:v,recaptchaV3Key:m,turnstileKey:R,...X,reaction:K===!0?Zt:K||null,imageUploader:ce($,Xt),highlighter:ce(S,na),texRenderer:ce(i,Yt),emoji:ce(E,Pe),search:ce(g,Jt(t))}),re=e=>typeof e==\"string\",Ae=\"{--waline-white:#000;--waline-light-grey:#666;--waline-dark-grey:#999;--waline-color:#888;--waline-bg-color:#1e1e1e;--waline-bg-color-light:#272727;--waline-bg-color-hover: #444;--waline-border-color:#333;--waline-disable-bg-color:#444;--waline-disable-color:#272727;--waline-bq-color:#272727;--waline-info-bg-color:#272727;--waline-info-color:#666}\",va=e=>re(e)?e===\"auto\"?`@media(prefers-color-scheme:dark){body${Ae}}`:`${e}${Ae}`:e===!0?`:root${Ae}`:\"\",Me=(e,l)=>{let t=e.toString();for(;t.length<l;)t=\"0\"+t;return t},da=e=>{const l=Me(e.getDate(),2),t=Me(e.getMonth()+1,2);return`${Me(e.getFullYear(),2)}-${t}-${l}`},pa=(e,l,t)=>{if(!e)return\"\";const a=re(e)?new Date(e.includes(\" \")?e.replace(/-/g,\"/\"):e):e,n=l.getTime()-a.getTime(),s=Math.floor(n/(24*3600*1e3));if(s===0){const h=n%864e5,c=Math.floor(h/(3600*1e3));if(c===0){const f=h%36e5,v=Math.floor(f/(60*1e3));if(v===0){const y=f%6e4;return`${Math.round(y/1e3)} ${t.seconds}`}return`${v} ${t.minutes}`}return`${c} ${t.hours}`}return s<0?t.now:s<8?`${s} ${t.days}`:da(a)},ga=/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,ha=e=>ga.test(e),fa=\"WALINE_EMOJI\",ot=(0,_vueuse_core__WEBPACK_IMPORTED_MODULE_6__.useStorage)(fa,{}),wa=e=>!!/@[0-9]+\\.[0-9]+\\.[0-9]+/.test(e),ya=e=>{const l=wa(e);if(l){const t=ot.value[e];if(t)return Promise.resolve(t)}return fetch(`${e}/info.json`).then(t=>t.json()).then(t=>{const a={folder:e,...t};return l&&(ot.value[e]=a),a})},it=(e,l=\"\",t=\"\",a=\"\")=>`${l?`${l}/`:\"\"}${t}${e}${a?`.${a}`:\"\"}`,ba=e=>Promise.all(e?e.map(l=>re(l)?ya(nt(l)):Promise.resolve(l)):[]).then(l=>{const t={tabs:[],map:{}};return l.forEach(a=>{const{name:n,folder:s,icon:h,prefix:c=\"\",type:f,items:v}=a;t.tabs.push({name:n,icon:it(h,s,c,f),items:v.map(y=>{const m=`${c}${y}`;return t.map[m]=it(y,s,c,f),m})})}),t}),st=e=>{e.name!==\"AbortError\"&&console.error(e.message)},xe=e=>e instanceof HTMLElement?e:re(e)?document.querySelector(e):null,ka=e=>e.type.includes(\"image\"),ct=e=>{const l=Array.from(e).find(ka);return l?l.getAsFile():null},Ca=/\\$.*?\\$/,$a=/^\\$(.*?)\\$/,La=/^(?:\\s{0,3})\\$\\$((?:[^\\n]|\\n[^\\n])+?)\\n{0,1}\\$\\$/,Ea=e=>[{name:\"blockMath\",level:\"block\",tokenizer(l){const t=La.exec(l);if(t!==null)return{type:\"html\",raw:t[0],text:e(!0,t[1])}}},{name:\"inlineMath\",level:\"inline\",start(l){const t=l.search(Ca);return t!==-1?t:l.length},tokenizer(l){const t=$a.exec(l);if(t!==null)return{type:\"html\",raw:t[0],text:e(!1,t[1])}}}],ut=(e=\"\",l={})=>e.replace(/:(.+?):/g,(t,a)=>l[a]?`<img class=\"wl-emoji\" src=\"${l[a]}\" alt=\"${a}\">`:t),Ia=(e,{emojiMap:l,highlighter:t,texRenderer:a})=>{const n=new marked__WEBPACK_IMPORTED_MODULE_3__.Marked;if(n.setOptions({breaks:!0}),t&&n.use((0,marked_highlight__WEBPACK_IMPORTED_MODULE_4__.markedHighlight)({highlight:t})),a){const s=Ea(a);n.use({extensions:s})}return n.parse(ut(e,l))},Re=e=>{const{path:l}=e.dataset;return l!=null&&l.length?l:null},Aa=e=>e.match(/[\\w\\d\\s,.\\u00C0-\\u024F\\u0400-\\u04FF]+/giu),Ma=e=>e.match(/[\\u4E00-\\u9FD5]/gu),xa=e=>{var l,t;return(((l=Aa(e))==null?void 0:l.reduce((a,n)=>a+([\"\",\",\",\".\"].includes(n.trim())?0:n.trim().split(/\\s+/u).length),0))??0)+(((t=Ma(e))==null?void 0:t.length)??0)},Ra=async()=>{const{userAgentData:e}=navigator;let l=navigator.userAgent;if(!e||e.platform!==\"Windows\")return l;const{platformVersion:t}=await e.getHighEntropyValues([\"platformVersion\"]);return t&&parseInt(t.split(\".\")[0])>=13&&(l=l.replace(\"Windows NT 10.0\",\"Windows NT 11.0\")),l},mt=({serverURL:e,path:l=window.location.pathname,selector:t=\".waline-comment-count\",lang:a=navigator.language})=>{const n=new AbortController,s=document.querySelectorAll(t);return s.length&&(0,_waline_api__WEBPACK_IMPORTED_MODULE_0__.fetchCommentCount)({serverURL:ye(e),paths:Array.from(s).map(h=>lt(Re(h)??l)),lang:a,signal:n.signal}).then(h=>{s.forEach((c,f)=>{c.innerText=h[f].toString()})}).catch(st),n.abort.bind(n)},vt=({size:e})=>(0,vue__WEBPACK_IMPORTED_MODULE_1__.h)(\"svg\",{class:\"wl-close-icon\",viewBox:\"0 0 1024 1024\",width:e,height:e},[(0,vue__WEBPACK_IMPORTED_MODULE_1__.h)(\"path\",{d:\"M697.173 85.333h-369.92c-144.64 0-241.92 101.547-241.92 252.587v348.587c0 150.613 97.28 252.16 241.92 252.16h369.92c144.64 0 241.494-101.547 241.494-252.16V337.92c0-151.04-96.854-252.587-241.494-252.587z\",fill:\"currentColor\"}),(0,vue__WEBPACK_IMPORTED_MODULE_1__.h)(\"path\",{d:\"m640.683 587.52-75.947-75.861 75.904-75.862a37.29 37.29 0 0 0 0-52.778 37.205 37.205 0 0 0-52.779 0l-75.946 75.818-75.862-75.946a37.419 37.419 0 0 0-52.821 0 37.419 37.419 0 0 0 0 52.821l75.947 75.947-75.776 75.733a37.29 37.29 0 1 0 52.778 52.821l75.776-75.776 75.947 75.947a37.376 37.376 0 0 0 52.779-52.821z\",fill:\"#888\"})]),Sa=()=>(0,vue__WEBPACK_IMPORTED_MODULE_1__.h)(\"svg\",{viewBox:\"0 0 1024 1024\",width:\"24\",height:\"24\"},(0,vue__WEBPACK_IMPORTED_MODULE_1__.h)(\"path\",{d:\"m341.013 394.667 27.755 393.45h271.83l27.733-393.45h64.106l-28.01 397.952a64 64 0 0 1-63.83 59.498H368.768a64 64 0 0 1-63.83-59.52l-28.053-397.93h64.128zm139.307 19.818v298.667h-64V414.485h64zm117.013 0v298.667h-64V414.485h64zM181.333 288h640v64h-640v-64zm453.483-106.667v64h-256v-64h256z\",fill:\"red\"})),ja=()=>(0,vue__WEBPACK_IMPORTED_MODULE_1__.h)(\"svg\",{viewBox:\"0 0 1024 1024\",width:\"24\",height:\"24\"},(0,vue__WEBPACK_IMPORTED_MODULE_1__.h)(\"path\",{d:\"M563.2 463.3 677 540c1.7 1.2 3.7 1.8 5.8 1.8.7 0 1.4-.1 2-.2 2.7-.5 5.1-2.1 6.6-4.4l25.3-37.8c1.5-2.3 2.1-5.1 1.6-7.8s-2.1-5.1-4.4-6.6l-73.6-49.1 73.6-49.1c2.3-1.5 3.9-3.9 4.4-6.6.5-2.7 0-5.5-1.6-7.8l-25.3-37.8a10.1 10.1 0 0 0-6.6-4.4c-.7-.1-1.3-.2-2-.2-2.1 0-4.1.6-5.8 1.8l-113.8 76.6c-9.2 6.2-14.7 16.4-14.7 27.5.1 11 5.5 21.3 14.7 27.4zM387 348.8h-45.5c-5.7 0-10.4 4.7-10.4 10.4v153.3c0 5.7 4.7 10.4 10.4 10.4H387c5.7 0 10.4-4.7 10.4-10.4V359.2c0-5.7-4.7-10.4-10.4-10.4zm333.8 241.3-41-20a10.3 10.3 0 0 0-8.1-.5c-2.6.9-4.8 2.9-5.9 5.4-30.1 64.9-93.1 109.1-164.4 115.2-5.7.5-9.9 5.5-9.5 11.2l3.9 45.5c.5 5.3 5 9.5 10.3 9.5h.9c94.8-8 178.5-66.5 218.6-152.7 2.4-5 .3-11.2-4.8-13.6zm186-186.1c-11.9-42-30.5-81.4-55.2-117.1-24.1-34.9-53.5-65.6-87.5-91.2-33.9-25.6-71.5-45.5-111.6-59.2-41.2-14-84.1-21.1-127.8-21.1h-1.2c-75.4 0-148.8 21.4-212.5 61.7-63.7 40.3-114.3 97.6-146.5 165.8-32.2 68.1-44.3 143.6-35.1 218.4 9.3 74.8 39.4 145 87.3 203.3.1.2.3.3.4.5l36.2 38.4c1.1 1.2 2.5 2.1 3.9 2.6 73.3 66.7 168.2 103.5 267.5 103.5 73.3 0 145.2-20.3 207.7-58.7 37.3-22.9 70.3-51.5 98.1-85 27.1-32.7 48.7-69.5 64.2-109.1 15.5-39.7 24.4-81.3 26.6-123.8 2.4-43.6-2.5-87-14.5-129zm-60.5 181.1c-8.3 37-22.8 72-43 104-19.7 31.1-44.3 58.6-73.1 81.7-28.8 23.1-61 41-95.7 53.4-35.6 12.7-72.9 19.1-110.9 19.1-82.6 0-161.7-30.6-222.8-86.2l-34.1-35.8c-23.9-29.3-42.4-62.2-55.1-97.7-12.4-34.7-18.8-71-19.2-107.9-.4-36.9 5.4-73.3 17.1-108.2 12-35.8 30-69.2 53.4-99.1 31.7-40.4 71.1-72 117.2-94.1 44.5-21.3 94-32.6 143.4-32.6 49.3 0 97 10.8 141.8 32 34.3 16.3 65.3 38.1 92 64.8 26.1 26 47.5 56 63.6 89.2 16.2 33.2 26.6 68.5 31 105.1 4.6 37.5 2.7 75.3-5.6 112.3z\",fill:\"currentColor\"})),Ua=()=>(0,vue__WEBPACK_IMPORTED_MODULE_1__.h)(\"svg\",{viewBox:\"0 0 1024 1024\",width:\"24\",height:\"24\"},[(0,vue__WEBPACK_IMPORTED_MODULE_1__.h)(\"path\",{d:\"M784 112H240c-88 0-160 72-160 160v480c0 88 72 160 160 160h544c88 0 160-72 160-160V272c0-88-72-160-160-160zm96 640c0 52.8-43.2 96-96 96H240c-52.8 0-96-43.2-96-96V272c0-52.8 43.2-96 96-96h544c52.8 0 96 43.2 96 96v480z\",fill:\"currentColor\"}),(0,vue__WEBPACK_IMPORTED_MODULE_1__.h)(\"path\",{d:\"M352 480c52.8 0 96-43.2 96-96s-43.2-96-96-96-96 43.2-96 96 43.2 96 96 96zm0-128c17.6 0 32 14.4 32 32s-14.4 32-32 32-32-14.4-32-32 14.4-32 32-32zm462.4 379.2-3.2-3.2-177.6-177.6c-25.6-25.6-65.6-25.6-91.2 0l-80 80-36.8-36.8c-25.6-25.6-65.6-25.6-91.2 0L200 728c-4.8 6.4-8 14.4-8 24 0 17.6 14.4 32 32 32 9.6 0 16-3.2 22.4-9.6L380.8 640l134.4 134.4c6.4 6.4 14.4 9.6 24 9.6 17.6 0 32-14.4 32-32 0-9.6-4.8-17.6-9.6-24l-52.8-52.8 80-80L769.6 776c6.4 4.8 12.8 8 20.8 8 17.6 0 32-14.4 32-32 0-8-3.2-16-8-20.8z\",fill:\"currentColor\"})]),za=({active:e=!1})=>(0,vue__WEBPACK_IMPORTED_MODULE_1__.h)(\"svg\",{viewBox:\"0 0 1024 1024\",width:\"24\",height:\"24\"},[(0,vue__WEBPACK_IMPORTED_MODULE_1__.h)(\"path\",{d:`M850.654 323.804c-11.042-25.625-26.862-48.532-46.885-68.225-20.022-19.61-43.258-34.936-69.213-45.73-26.78-11.124-55.124-16.727-84.375-16.727-40.622 0-80.256 11.123-114.698 32.135A214.79 214.79 0 0 0 512 241.819a214.79 214.79 0 0 0-23.483-16.562c-34.442-21.012-74.076-32.135-114.698-32.135-29.25 0-57.595 5.603-84.375 16.727-25.872 10.711-49.19 26.12-69.213 45.73-20.105 19.693-35.843 42.6-46.885 68.225-11.453 26.615-17.303 54.877-17.303 83.963 0 27.439 5.603 56.03 16.727 85.117 9.31 24.307 22.659 49.52 39.715 74.981 27.027 40.293 64.188 82.316 110.33 124.915 76.465 70.615 152.189 119.394 155.402 121.371l19.528 12.525c8.652 5.52 19.776 5.52 28.427 0l19.529-12.525c3.213-2.06 78.854-50.756 155.401-121.371 46.143-42.6 83.304-84.622 110.33-124.915 17.057-25.46 30.487-50.674 39.716-74.981 11.124-29.087 16.727-57.678 16.727-85.117.082-29.086-5.768-57.348-17.221-83.963z${e?\"\":\"M512 761.5S218.665 573.55 218.665 407.767c0-83.963 69.461-152.023 155.154-152.023 60.233 0 112.473 33.618 138.181 82.727 25.708-49.109 77.948-82.727 138.18-82.727 85.694 0 155.155 68.06 155.155 152.023C805.335 573.551 512 761.5 512 761.5z\"}`,fill:e?\"red\":\"currentColor\"})]),Va=()=>(0,vue__WEBPACK_IMPORTED_MODULE_1__.h)(\"svg\",{viewBox:\"0 0 1024 1024\",width:\"24\",height:\"24\"},[(0,vue__WEBPACK_IMPORTED_MODULE_1__.h)(\"path\",{d:\"M710.816 654.301c70.323-96.639 61.084-230.578-23.705-314.843-46.098-46.098-107.183-71.109-172.28-71.109-65.008 0-126.092 25.444-172.28 71.109-45.227 46.098-70.756 107.183-70.756 172.106 0 64.923 25.444 126.007 71.194 172.106 46.099 46.098 107.184 71.109 172.28 71.109 51.414 0 100.648-16.212 142.824-47.404l126.53 126.006c7.058 7.06 16.297 10.979 26.406 10.979 10.105 0 19.343-3.919 26.402-10.979 14.467-14.467 14.467-38.172 0-52.723L710.816 654.301zm-315.107-23.265c-65.88-65.88-65.88-172.54 0-238.42 32.069-32.07 74.245-49.149 119.471-49.149 45.227 0 87.407 17.603 119.472 49.149 65.88 65.879 65.88 172.539 0 238.42-63.612 63.178-175.242 63.178-238.943 0zm0 0\",fill:\"currentColor\"}),(0,vue__WEBPACK_IMPORTED_MODULE_1__.h)(\"path\",{d:\"M703.319 121.603H321.03c-109.8 0-199.469 89.146-199.469 199.38v382.034c0 109.796 89.236 199.38 199.469 199.38h207.397c20.653 0 37.384-16.645 37.384-37.299 0-20.649-16.731-37.296-37.384-37.296H321.03c-68.582 0-124.352-55.77-124.352-124.267V321.421c0-68.496 55.77-124.267 124.352-124.267h382.289c68.582 0 124.352 55.771 124.352 124.267V524.72c0 20.654 16.736 37.299 37.385 37.299 20.654 0 37.384-16.645 37.384-37.299V320.549c-.085-109.8-89.321-198.946-199.121-198.946zm0 0\",fill:\"currentColor\"})]),_a=()=>(0,vue__WEBPACK_IMPORTED_MODULE_1__.h)(\"svg\",{width:\"16\",height:\"16\",ariaHidden:\"true\"},(0,vue__WEBPACK_IMPORTED_MODULE_1__.h)(\"path\",{d:\"M14.85 3H1.15C.52 3 0 3.52 0 4.15v7.69C0 12.48.52 13 1.15 13h13.69c.64 0 1.15-.52 1.15-1.15v-7.7C16 3.52 15.48 3 14.85 3zM9 11H7V8L5.5 9.92 4 8v3H2V5h2l1.5 2L7 5h2v6zm2.99.5L9.5 8H11V5h2v3h1.5l-2.51 3.5z\",fill:\"currentColor\"})),Ha=()=>(0,vue__WEBPACK_IMPORTED_MODULE_1__.h)(\"svg\",{viewBox:\"0 0 1024 1024\",width:\"24\",height:\"24\"},(0,vue__WEBPACK_IMPORTED_MODULE_1__.h)(\"path\",{d:\"M810.667 213.333a64 64 0 0 1 64 64V704a64 64 0 0 1-64 64H478.336l-146.645 96.107a21.333 21.333 0 0 1-33.024-17.856V768h-85.334a64 64 0 0 1-64-64V277.333a64 64 0 0 1 64-64h597.334zm0 64H213.333V704h149.334v63.296L459.243 704h351.424V277.333zm-271.36 213.334v64h-176.64v-64h176.64zm122.026-128v64H362.667v-64h298.666z\",fill:\"currentColor\"})),Ta=()=>(0,vue__WEBPACK_IMPORTED_MODULE_1__.h)(\"svg\",{viewBox:\"0 0 1024 1024\",width:\"24\",height:\"24\"},(0,vue__WEBPACK_IMPORTED_MODULE_1__.h)(\"path\",{d:\"M813.039 318.772L480.53 651.278H360.718V531.463L693.227 198.961C697.904 194.284 704.027 192 710.157 192C716.302 192 722.436 194.284 727.114 198.961L813.039 284.88C817.72 289.561 820 295.684 820 301.825C820 307.95 817.72 314.093 813.039 318.772ZM710.172 261.888L420.624 551.431V591.376H460.561L750.109 301.825L710.172 261.888ZM490.517 291.845H240.906V771.09H720.156V521.479C720.156 504.947 733.559 491.529 750.109 491.529C766.653 491.529 780.063 504.947 780.063 521.479V791.059C780.063 813.118 762.18 831 740.125 831H220.937C198.882 831 181 813.118 181 791.059V271.872C181 249.817 198.882 231.935 220.937 231.935H490.517C507.06 231.935 520.47 245.352 520.47 261.888C520.47 278.424 507.06 291.845 490.517 291.845Z\",fill:\"currentColor\"})),Fa=()=>(0,vue__WEBPACK_IMPORTED_MODULE_1__.h)(\"svg\",{class:\"verified-icon\",viewBox:\"0 0 1024 1024\",width:\"14\",height:\"14\"},(0,vue__WEBPACK_IMPORTED_MODULE_1__.h)(\"path\",{d:\"m894.4 461.56-54.4-63.2c-10.4-12-18.8-34.4-18.8-50.4v-68c0-42.4-34.8-77.2-77.2-77.2h-68c-15.6 0-38.4-8.4-50.4-18.8l-63.2-54.4c-27.6-23.6-72.8-23.6-100.8 0l-62.8 54.8c-12 10-34.8 18.4-50.4 18.4h-69.2c-42.4 0-77.2 34.8-77.2 77.2v68.4c0 15.6-8.4 38-18.4 50l-54 63.6c-23.2 27.6-23.2 72.4 0 100l54 63.6c10 12 18.4 34.4 18.4 50v68.4c0 42.4 34.8 77.2 77.2 77.2h69.2c15.6 0 38.4 8.4 50.4 18.8l63.2 54.4c27.6 23.6 72.8 23.6 100.8 0l63.2-54.4c12-10.4 34.4-18.8 50.4-18.8h68c42.4 0 77.2-34.8 77.2-77.2v-68c0-15.6 8.4-38.4 18.8-50.4l54.4-63.2c23.2-27.6 23.2-73.2-.4-100.8zm-216-25.2-193.2 193.2a30 30 0 0 1-42.4 0l-96.8-96.8a30.16 30.16 0 0 1 0-42.4c11.6-11.6 30.8-11.6 42.4 0l75.6 75.6 172-172c11.6-11.6 30.8-11.6 42.4 0 11.6 11.6 11.6 30.8 0 42.4z\",fill:\"#27ae60\"})),ue=({size:e=100})=>(0,vue__WEBPACK_IMPORTED_MODULE_1__.h)(\"svg\",{width:e,height:e,viewBox:\"0 0 100 100\",preserveAspectRatio:\"xMidYMid\"},(0,vue__WEBPACK_IMPORTED_MODULE_1__.h)(\"circle\",{cx:50,cy:50,fill:\"none\",stroke:\"currentColor\",strokeWidth:\"4\",r:\"40\",\"stroke-dasharray\":\"85 30\"},(0,vue__WEBPACK_IMPORTED_MODULE_1__.h)(\"animateTransform\",{attributeName:\"transform\",type:\"rotate\",repeatCount:\"indefinite\",dur:\"1s\",values:\"0 50 50;360 50 50\",keyTimes:\"0;1\"}))),Na=()=>(0,vue__WEBPACK_IMPORTED_MODULE_1__.h)(\"svg\",{width:24,height:24,fill:\"currentcolor\",viewBox:\"0 0 24 24\"},[(0,vue__WEBPACK_IMPORTED_MODULE_1__.h)(\"path\",{style:\"transform: translateY(0.5px)\",d:\"M18.968 10.5H15.968V11.484H17.984V12.984H15.968V15H14.468V9H18.968V10.5V10.5ZM8.984 9C9.26533 9 9.49967 9.09367 9.687 9.281C9.87433 9.46833 9.968 9.70267 9.968 9.984V10.5H6.499V13.5H8.468V12H9.968V14.016C9.968 14.2973 9.87433 14.5317 9.687 14.719C9.49967 14.9063 9.26533 15 8.984 15H5.984C5.70267 15 5.46833 14.9063 5.281 14.719C5.09367 14.5317 5 14.2973 5 14.016V9.985C5 9.70367 5.09367 9.46933 5.281 9.282C5.46833 9.09467 5.70267 9.001 5.984 9.001H8.984V9ZM11.468 9H12.968V15H11.468V9V9Z\"}),(0,vue__WEBPACK_IMPORTED_MODULE_1__.h)(\"path\",{d:\"M18.5 3H5.75C3.6875 3 2 4.6875 2 6.75V18C2 20.0625 3.6875 21.75 5.75 21.75H18.5C20.5625 21.75 22.25 20.0625 22.25 18V6.75C22.25 4.6875 20.5625 3 18.5 3ZM20.75 18C20.75 19.2375 19.7375 20.25 18.5 20.25H5.75C4.5125 20.25 3.5 19.2375 3.5 18V6.75C3.5 5.5125 4.5125 4.5 5.75 4.5H18.5C19.7375 4.5 20.75 5.5125 20.75 6.75V18Z\"})]),Wa=()=>(0,_vueuse_core__WEBPACK_IMPORTED_MODULE_6__.useStorage)(\"WALINE_USER_META\",{nick:\"\",mail:\"\",link:\"\"}),Ba=()=>(0,_vueuse_core__WEBPACK_IMPORTED_MODULE_6__.useStorage)(\"WALINE_COMMENT_BOX_EDITOR\",\"\"),Da=\"WALINE_LIKE\",Pa=(0,_vueuse_core__WEBPACK_IMPORTED_MODULE_6__.useStorage)(Da,[]),dt=()=>Pa,qa=\"WALINE_REACTION\",Oa=(0,_vueuse_core__WEBPACK_IMPORTED_MODULE_6__.useStorage)(qa,{}),Ga=()=>Oa,pt={},Ka=e=>{const l=pt[e]??(pt[e]=(0,recaptcha_v3__WEBPACK_IMPORTED_MODULE_5__.load)(e,{useRecaptchaNet:!0,autoHideBadge:!0}));return{execute:t=>l.then(a=>a.execute(t))}},Za=e=>({execute:async l=>{const{load:t}=(0,_vueuse_core__WEBPACK_IMPORTED_MODULE_6__.useScriptTag)(\"https://challenges.cloudflare.com/turnstile/v0/api.js\",void 0,{async:!1});await t();const a=window.turnstile;return new Promise(n=>{a==null||a.ready(()=>{a.render(\".wl-captcha-container\",{sitekey:e,action:l,size:\"compact\",callback:n})})})}}),Xa=\"WALINE_USER\",Ya=(0,_vueuse_core__WEBPACK_IMPORTED_MODULE_6__.useStorage)(Xa,{}),be=()=>Ya,Ja={key:0,class:\"wl-reaction\"},Qa=[\"textContent\"],el={class:\"wl-reaction-list\"},tl=[\"onClick\"],al={class:\"wl-reaction-img\"},ll=[\"src\",\"alt\"],nl=[\"textContent\"],rl=[\"textContent\"];var ol=(0,vue__WEBPACK_IMPORTED_MODULE_1__.defineComponent)({__name:\"ArticleReaction\",setup(e){const l=Ga(),t=(0,vue__WEBPACK_IMPORTED_MODULE_1__.inject)(we),a=(0,vue__WEBPACK_IMPORTED_MODULE_1__.ref)(-1),n=(0,vue__WEBPACK_IMPORTED_MODULE_1__.ref)([]),s=(0,vue__WEBPACK_IMPORTED_MODULE_1__.computed)(()=>t.value.locale),h=(0,vue__WEBPACK_IMPORTED_MODULE_1__.computed)(()=>{const{reaction:m}=t.value;return m!=null&&m.length?m:null}),c=(0,vue__WEBPACK_IMPORTED_MODULE_1__.computed)(()=>{var m;const{path:R}=t.value;return((m=h.value)==null?void 0:m.map((j,E)=>({icon:j,desc:s.value[`reaction${E}`],active:l.value[R]===E})))??null});let f;const v=async()=>{const{serverURL:m,lang:R,path:j}=t.value;if(!h.value)return;const E=new AbortController;f=E.abort.bind(E);const[$]=await (0,_waline_api__WEBPACK_IMPORTED_MODULE_0__.getArticleCounter)({serverURL:m,lang:R,paths:[j],type:h.value.map((S,i)=>`reaction${i}`),signal:E.signal});n.value=h.value.map((S,i)=>$[`reaction${i}`])},y=async m=>{if(a.value!==-1)return;const{serverURL:R,lang:j,path:E}=t.value,$=l.value[E];a.value=m,$!==void 0&&(await (0,_waline_api__WEBPACK_IMPORTED_MODULE_0__.updateArticleCounter)({serverURL:R,lang:j,path:E,type:`reaction${$}`,action:\"desc\"}),n.value[$]=Math.max(n.value[$]-1,0)),$!==m&&(await (0,_waline_api__WEBPACK_IMPORTED_MODULE_0__.updateArticleCounter)({serverURL:R,lang:j,path:E,type:`reaction${m}`}),n.value[m]=(n.value[m]||0)+1),$===m?delete l.value[E]:l.value[E]=m,a.value=-1};return (0,vue__WEBPACK_IMPORTED_MODULE_1__.onMounted)(()=>{(0,_vueuse_core__WEBPACK_IMPORTED_MODULE_7__.watchImmediate)(()=>[t.value.serverURL,t.value.path],()=>v())}),(0,vue__WEBPACK_IMPORTED_MODULE_1__.onUnmounted)(()=>{f==null||f()}),(m,R)=>c.value?((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"div\",Ja,[(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"div\",{class:\"wl-reaction-title\",textContent:(0,vue__WEBPACK_IMPORTED_MODULE_1__.toDisplayString)(s.value.reactionTitle)},null,8,Qa),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"ul\",el,[((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(!0),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(vue__WEBPACK_IMPORTED_MODULE_1__.Fragment,null,(0,vue__WEBPACK_IMPORTED_MODULE_1__.renderList)(c.value,({active:j,icon:E,desc:$},S)=>((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"li\",{key:S,class:(0,vue__WEBPACK_IMPORTED_MODULE_1__.normalizeClass)([\"wl-reaction-item\",{active:j}]),onClick:i=>y(S)},[(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"div\",al,[(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"img\",{src:E,alt:$},null,8,ll),a.value===S?((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createBlock)((0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(ue),{key:0,class:\"wl-reaction-loading\"})):((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"div\",{key:1,class:\"wl-reaction-votes\",textContent:(0,vue__WEBPACK_IMPORTED_MODULE_1__.toDisplayString)(n.value[S]||0)},null,8,nl))]),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"div\",{class:\"wl-reaction-text\",textContent:(0,vue__WEBPACK_IMPORTED_MODULE_1__.toDisplayString)($)},null,8,rl)],10,tl))),128))])])):(0,vue__WEBPACK_IMPORTED_MODULE_1__.createCommentVNode)(\"v-if\",!0)}});const il=[\"data-index\"],sl=[\"src\",\"title\",\"onClick\"];var cl=(0,vue__WEBPACK_IMPORTED_MODULE_1__.defineComponent)({__name:\"ImageWall\",props:{items:{default:()=>[]},columnWidth:{default:300},gap:{default:0}},emits:[\"insert\"],setup(e){const l=e;let t=null;const a=(0,vue__WEBPACK_IMPORTED_MODULE_1__.useTemplateRef)(\"wall\"),n=(0,vue__WEBPACK_IMPORTED_MODULE_1__.ref)({}),s=(0,vue__WEBPACK_IMPORTED_MODULE_1__.ref)([]),h=()=>{const m=Math.floor((a.value.getBoundingClientRect().width+l.gap)/(l.columnWidth+l.gap));return m>0?m:1},c=m=>new Array(m).fill(null).map(()=>[]),f=async m=>{var R;if(m>=l.items.length)return;await (0,vue__WEBPACK_IMPORTED_MODULE_1__.nextTick)();const j=Array.from(((R=a.value)==null?void 0:R.children)??[]).reduce((E,$)=>$.getBoundingClientRect().height<E.getBoundingClientRect().height?$:E);s.value[Number(j.dataset.index)].push(m),await f(m+1)},v=async(m=!1)=>{if(s.value.length===h()&&!m)return;s.value=c(h());const R=window.scrollY;await f(0),window.scrollTo({top:R})},y=m=>{n.value[m.target.src]=!0};return (0,vue__WEBPACK_IMPORTED_MODULE_1__.onMounted)(()=>{v(!0),t=new ResizeObserver(()=>{v()}),t.observe(a.value),(0,vue__WEBPACK_IMPORTED_MODULE_1__.watch)(()=>[l.items],()=>{n.value={},v(!0)}),(0,vue__WEBPACK_IMPORTED_MODULE_1__.watch)(()=>[l.columnWidth,l.gap],()=>{v()})}),(0,vue__WEBPACK_IMPORTED_MODULE_1__.onBeforeUnmount)(()=>{t.unobserve(a.value)}),(m,R)=>((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"div\",{ref_key:\"wall\",ref:a,class:\"wl-gallery\",style:(0,vue__WEBPACK_IMPORTED_MODULE_1__.normalizeStyle)({gap:`${m.gap}px`})},[((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(!0),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(vue__WEBPACK_IMPORTED_MODULE_1__.Fragment,null,(0,vue__WEBPACK_IMPORTED_MODULE_1__.renderList)(s.value,(j,E)=>((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"div\",{key:E,class:\"wl-gallery-column\",\"data-index\":E,style:(0,vue__WEBPACK_IMPORTED_MODULE_1__.normalizeStyle)({gap:`${m.gap}px`})},[((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(!0),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(vue__WEBPACK_IMPORTED_MODULE_1__.Fragment,null,(0,vue__WEBPACK_IMPORTED_MODULE_1__.renderList)(j,$=>((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(vue__WEBPACK_IMPORTED_MODULE_1__.Fragment,{key:$},[n.value[m.items[$].src]?(0,vue__WEBPACK_IMPORTED_MODULE_1__.createCommentVNode)(\"v-if\",!0):((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createBlock)((0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(ue),{key:0,size:36,style:{margin:\"20px auto\"}})),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"img\",{class:\"wl-gallery-item\",src:m.items[$].src,title:m.items[$].title,loading:\"lazy\",onLoad:y,onClick:S=>m.$emit(\"insert\",`![](${m.items[$].src})`)},null,40,sl)],64))),128))],12,il))),128))],4))}});const ul={key:0,class:\"wl-login-info\"},ml={class:\"wl-avatar\"},vl=[\"title\"],dl=[\"title\"],pl=[\"src\"],gl=[\"title\",\"textContent\"],hl={class:\"wl-panel\"},fl=[\"for\",\"textContent\"],wl=[\"id\",\"onUpdate:modelValue\",\"name\",\"type\"],yl=[\"placeholder\"],bl={class:\"wl-preview\"},kl=[\"innerHTML\"],Cl={class:\"wl-footer\"},$l={class:\"wl-actions\"},Ll={href:\"https://guides.github.com/features/mastering-markdown/\",title:\"Markdown Guide\",\"aria-label\":\"Markdown is supported\",class:\"wl-action\",target:\"_blank\",rel:\"noopener noreferrer\"},El=[\"title\"],Il=[\"title\"],Al=[\"title\",\"aria-label\"],Ml=[\"title\"],xl={class:\"wl-info\"},Rl={class:\"wl-text-number\"},Sl={key:0},jl=[\"textContent\"],Ul=[\"textContent\"],zl=[\"disabled\"],Vl=[\"placeholder\"],_l={key:1,class:\"wl-loading\"},Hl={key:0,class:\"wl-tab-wrapper\"},Tl=[\"title\",\"onClick\"],Fl=[\"src\",\"alt\"],Nl={key:0,class:\"wl-tabs\"},Wl=[\"onClick\"],Bl=[\"src\",\"alt\",\"title\"],Dl=[\"title\"];var gt=(0,vue__WEBPACK_IMPORTED_MODULE_1__.defineComponent)({__name:\"CommentBox\",props:{edit:{},rootId:{},replyId:{},replyUser:{}},emits:[\"log\",\"cancelEdit\",\"cancelReply\",\"submit\"],setup(e,{emit:l}){const t=e,a=l,n=(0,vue__WEBPACK_IMPORTED_MODULE_1__.inject)(we),s=Ba(),h=Wa(),c=be(),f=(0,vue__WEBPACK_IMPORTED_MODULE_1__.ref)({}),v=(0,vue__WEBPACK_IMPORTED_MODULE_1__.useTemplateRef)(\"textarea\"),y=(0,vue__WEBPACK_IMPORTED_MODULE_1__.useTemplateRef)(\"image-uploader\"),m=(0,vue__WEBPACK_IMPORTED_MODULE_1__.useTemplateRef)(\"emoji-button\"),R=(0,vue__WEBPACK_IMPORTED_MODULE_1__.useTemplateRef)(\"emoji-popup\"),j=(0,vue__WEBPACK_IMPORTED_MODULE_1__.useTemplateRef)(\"gif-button\"),E=(0,vue__WEBPACK_IMPORTED_MODULE_1__.useTemplateRef)(\"gif-popup\"),$=(0,vue__WEBPACK_IMPORTED_MODULE_1__.useTemplateRef)(\"gif-search\"),S=(0,vue__WEBPACK_IMPORTED_MODULE_1__.ref)({tabs:[],map:{}}),i=(0,vue__WEBPACK_IMPORTED_MODULE_1__.ref)(0),g=(0,vue__WEBPACK_IMPORTED_MODULE_1__.ref)(!1),K=(0,vue__WEBPACK_IMPORTED_MODULE_1__.ref)(!1),X=(0,vue__WEBPACK_IMPORTED_MODULE_1__.ref)(!1),A=(0,vue__WEBPACK_IMPORTED_MODULE_1__.ref)(\"\"),V=(0,vue__WEBPACK_IMPORTED_MODULE_1__.ref)(0),T=(0,vue__WEBPACK_IMPORTED_MODULE_1__.reactive)({loading:!0,list:[]}),le=(0,vue__WEBPACK_IMPORTED_MODULE_1__.ref)(0),Q=(0,vue__WEBPACK_IMPORTED_MODULE_1__.ref)(!1),me=(0,vue__WEBPACK_IMPORTED_MODULE_1__.ref)(\"\"),w=(0,vue__WEBPACK_IMPORTED_MODULE_1__.ref)(!1),U=(0,vue__WEBPACK_IMPORTED_MODULE_1__.ref)(!1),b=(0,vue__WEBPACK_IMPORTED_MODULE_1__.computed)(()=>n.value.locale),F=(0,vue__WEBPACK_IMPORTED_MODULE_1__.computed)(()=>!!c.value.token),q=(0,vue__WEBPACK_IMPORTED_MODULE_1__.computed)(()=>n.value.imageUploader!==null),O=d=>{const r=v.value,C=r.selectionStart,H=r.selectionEnd||0,k=r.scrollTop;s.value=r.value.substring(0,C)+d+r.value.substring(H,r.value.length),r.focus(),r.selectionStart=C+d.length,r.selectionEnd=C+d.length,r.scrollTop=k},oe=({key:d,ctrlKey:r,metaKey:C})=>{w.value||(r||C)&&d===\"Enter\"&&Se()},ve=async d=>{const r=`![${n.value.locale.uploading} ${d.name}]()`;O(r),w.value=!0;try{const C=await n.value.imageUploader(d);s.value=s.value.replace(r,`\\r\n![${d.name}](${C})`)}catch(C){alert(C.message),s.value=s.value.replace(r,\"\")}finally{w.value=!1}},ke=d=>{var r;if((r=d.dataTransfer)!=null&&r.items){const C=ct(d.dataTransfer.items);C&&q.value&&(ve(C),d.preventDefault())}},yt=d=>{if(d.clipboardData){const r=ct(d.clipboardData.items);r&&q.value&&ve(r)}},bt=()=>{const d=y.value;d.files&&q.value&&ve(d.files[0]).then(()=>{d.value=\"\"})},Se=async()=>{var d;const{serverURL:r,lang:C,login:H,wordLimit:k,requiredMeta:D,recaptchaV3Key:N,turnstileKey:Y}=n.value,_={comment:me.value,nick:h.value.nick,mail:h.value.mail,link:h.value.link,url:n.value.path,ua:await Ra()};if(!t.edit)if(c.value.token)_.nick=c.value.display_name,_.mail=c.value.email,_.link=c.value.url;else{if(H===\"force\")return;if(D.includes(\"nick\")&&!_.nick){f.value.nick.focus(),alert(b.value.nickError);return}if(D.includes(\"mail\")&&!_.mail||_.mail&&!ha(_.mail)){f.value.mail.focus(),alert(b.value.mailError);return}_.nick||(_.nick=b.value.anonymous)}if(!_.comment){v.value.focus();return}if(!Q.value){alert(b.value.wordHint.replace(\"$0\",k[0].toString()).replace(\"$1\",k[1].toString()).replace(\"$2\",V.value.toString()));return}_.comment=ut(_.comment,S.value.map),t.replyId&&t.rootId&&(_.pid=t.replyId,_.rid=t.rootId,_.at=t.replyUser),w.value=!0;try{N&&(_.recaptchaV3=await Ka(N).execute(\"social\")),Y&&(_.turnstile=await Za(Y).execute(\"social\"));const de={serverURL:r,lang:C,token:c.value.token,comment:_},Ce=await(t.edit?(0,_waline_api__WEBPACK_IMPORTED_MODULE_0__.updateComment)({objectId:t.edit.objectId,...de}):(0,_waline_api__WEBPACK_IMPORTED_MODULE_0__.addComment)(de));if(w.value=!1,Ce.errmsg){alert(Ce.errmsg);return}a(\"submit\",Ce.data),s.value=\"\",A.value=\"\",await (0,vue__WEBPACK_IMPORTED_MODULE_1__.nextTick)(),t.replyId&&a(\"cancelReply\"),(d=t.edit)!=null&&d.objectId&&a(\"cancelEdit\")}catch(de){w.value=!1,alert(de.message)}},kt=d=>{d.preventDefault();const{lang:r,serverURL:C}=n.value;(0,_waline_api__WEBPACK_IMPORTED_MODULE_0__.login)({serverURL:C,lang:r}).then(H=>{c.value=H,(H.remember?localStorage:sessionStorage).setItem(\"WALINE_USER\",JSON.stringify(H)),a(\"log\")})},Ct=()=>{c.value={},localStorage.setItem(\"WALINE_USER\",\"null\"),sessionStorage.setItem(\"WALINE_USER\",\"null\"),a(\"log\")},je=d=>{d.preventDefault();const{lang:r,serverURL:C}=n.value,H=800,k=800,D=(window.innerWidth-H)/2,N=(window.innerHeight-k)/2,Y=new URLSearchParams({lng:r,token:c.value.token}),_=window.open(`${C}/ui/profile?${Y.toString()}`,\"_blank\",`width=${H},height=${k},left=${D},top=${N},scrollbars=no,resizable=no,status=no,location=no,toolbar=no,menubar=no`);_==null||_.postMessage({type:\"TOKEN\",data:c.value.token},\"*\")},$t=d=>{var r,C,H,k;!((r=m.value)!=null&&r.contains(d.target))&&!((C=R.value)!=null&&C.contains(d.target))&&(g.value=!1),!((H=j.value)!=null&&H.contains(d.target))&&!((k=E.value)!=null&&k.contains(d.target))&&(K.value=!1)},Ue=async d=>{var r;const{scrollTop:C,clientHeight:H,scrollHeight:k}=d.target,D=(H+C)/k,N=n.value.search,Y=((r=$.value)==null?void 0:r.value)??\"\";D<.9||T.loading||U.value||(T.loading=!0,(N.more&&T.list.length?await N.more(Y,T.list.length):await N.search(Y)).length?T.list=[...T.list,...N.more&&T.list.length?await N.more(Y,T.list.length):await N.search(Y)]:U.value=!0,T.loading=!1,setTimeout(()=>{d.target.scrollTop=C},50))},ze=(0,_vueuse_core__WEBPACK_IMPORTED_MODULE_7__.useDebounceFn)(d=>{T.list=[],U.value=!1,Ue(d)},300);return (0,_vueuse_core__WEBPACK_IMPORTED_MODULE_6__.useEventListener)(\"click\",$t),(0,_vueuse_core__WEBPACK_IMPORTED_MODULE_6__.useEventListener)(\"message\",({data:d})=>{!d||d.type!==\"profile\"||(c.value={...c.value,...d.data},[localStorage,sessionStorage].filter(r=>r.getItem(\"WALINE_USER\")).forEach(r=>{r.setItem(\"WALINE_USER\",JSON.stringify(c))}))}),(0,_vueuse_core__WEBPACK_IMPORTED_MODULE_7__.watchImmediate)([n,V],([d,r])=>{const{wordLimit:C}=d;C?r<C[0]&&C[0]!==0?(le.value=C[0],Q.value=!1):r>C[1]?(le.value=C[1],Q.value=!1):(le.value=C[1],Q.value=!0):(le.value=0,Q.value=!0)}),(0,vue__WEBPACK_IMPORTED_MODULE_1__.watch)(K,async d=>{var r;if(!d)return;const C=n.value.search;$.value&&($.value.value=\"\"),T.loading=!0,T.list=await(((r=C.default)==null?void 0:r.call(C))??C.search(\"\")),T.loading=!1}),(0,vue__WEBPACK_IMPORTED_MODULE_1__.onMounted)(()=>{var d;(d=t.edit)!=null&&d.objectId&&(s.value=t.edit.orig),(0,_vueuse_core__WEBPACK_IMPORTED_MODULE_7__.watchImmediate)(()=>s.value,r=>{const{highlighter:C,texRenderer:H}=n.value;me.value=r,A.value=Ia(r,{emojiMap:S.value.map,highlighter:C,texRenderer:H}),V.value=xa(r),r?(0,autosize__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(v.value):autosize__WEBPACK_IMPORTED_MODULE_2__[\"default\"].destroy(v.value)}),(0,_vueuse_core__WEBPACK_IMPORTED_MODULE_7__.watchImmediate)(()=>n.value.emoji,async r=>{S.value=await ba(r)})}),(d,r)=>{var C,H;return (0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"div\",{key:(0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(c).token,class:\"wl-comment\"},[(0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(n).login!==\"disable\"&&F.value&&!((C=d.edit)!=null&&C.objectId)?((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"div\",ul,[(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"div\",ml,[(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"button\",{type:\"submit\",class:\"wl-logout-btn\",title:b.value.logout,onClick:Ct},[(0,vue__WEBPACK_IMPORTED_MODULE_1__.createVNode)((0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(vt),{size:14})],8,vl),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"a\",{href:\"#\",class:\"wl-login-nick\",\"aria-label\":\"Profile\",title:b.value.profile,onClick:je},[(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"img\",{src:(0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(c).avatar,alt:\"avatar\"},null,8,pl)],8,dl)]),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"a\",{href:\"#\",class:\"wl-login-nick\",\"aria-label\":\"Profile\",title:b.value.profile,onClick:je,textContent:(0,vue__WEBPACK_IMPORTED_MODULE_1__.toDisplayString)((0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(c).display_name)},null,8,gl)])):(0,vue__WEBPACK_IMPORTED_MODULE_1__.createCommentVNode)(\"v-if\",!0),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"div\",hl,[(0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(n).login!==\"force\"&&(0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(n).meta.length&&!F.value?((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"div\",{key:0,class:(0,vue__WEBPACK_IMPORTED_MODULE_1__.normalizeClass)([\"wl-header\",`item${(0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(n).meta.length}`])},[((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(!0),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(vue__WEBPACK_IMPORTED_MODULE_1__.Fragment,null,(0,vue__WEBPACK_IMPORTED_MODULE_1__.renderList)((0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(n).meta,k=>((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"div\",{key:k,class:\"wl-header-item\"},[(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"label\",{for:`wl-${k}`,textContent:(0,vue__WEBPACK_IMPORTED_MODULE_1__.toDisplayString)(b.value[k]+((0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(n).requiredMeta.includes(k)||!(0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(n).requiredMeta.length?\"\":`(${b.value.optional})`))},null,8,fl),(0,vue__WEBPACK_IMPORTED_MODULE_1__.withDirectives)((0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"input\",{id:`wl-${k}`,ref_for:!0,ref:D=>{D&&(f.value[k]=D)},\"onUpdate:modelValue\":D=>(0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(h)[k]=D,class:(0,vue__WEBPACK_IMPORTED_MODULE_1__.normalizeClass)([\"wl-input\",`wl-${k}`]),name:k,type:k===\"mail\"?\"email\":\"text\"},null,10,wl),[[vue__WEBPACK_IMPORTED_MODULE_1__.vModelDynamic,(0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(h)[k]]])]))),128))],2)):(0,vue__WEBPACK_IMPORTED_MODULE_1__.createCommentVNode)(\"v-if\",!0),(0,vue__WEBPACK_IMPORTED_MODULE_1__.withDirectives)((0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"textarea\",{id:\"wl-edit\",ref:\"textarea\",\"onUpdate:modelValue\":r[0]||(r[0]=k=>(0,vue__WEBPACK_IMPORTED_MODULE_1__.isRef)(s)?s.value=k:null),class:\"wl-editor\",placeholder:d.replyUser?`@${d.replyUser}`:b.value.placeholder,onKeydown:oe,onDrop:ke,onPaste:yt},null,40,yl),[[vue__WEBPACK_IMPORTED_MODULE_1__.vModelText,(0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(s)]]),(0,vue__WEBPACK_IMPORTED_MODULE_1__.withDirectives)((0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"div\",bl,[r[7]||(r[7]=(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"hr\",null,null,-1)),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"h4\",null,(0,vue__WEBPACK_IMPORTED_MODULE_1__.toDisplayString)(b.value.preview)+\":\",1),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"div\",{class:\"wl-content\",innerHTML:A.value},null,8,kl)],512),[[vue__WEBPACK_IMPORTED_MODULE_1__.vShow,X.value]]),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"div\",Cl,[(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"div\",$l,[(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"a\",Ll,[(0,vue__WEBPACK_IMPORTED_MODULE_1__.createVNode)((0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(_a))]),(0,vue__WEBPACK_IMPORTED_MODULE_1__.withDirectives)((0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"button\",{ref:\"emoji-button\",type:\"button\",class:(0,vue__WEBPACK_IMPORTED_MODULE_1__.normalizeClass)([\"wl-action\",{active:g.value}]),title:b.value.emoji,onClick:r[1]||(r[1]=k=>g.value=!g.value)},[(0,vue__WEBPACK_IMPORTED_MODULE_1__.createVNode)((0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(ja))],10,El),[[vue__WEBPACK_IMPORTED_MODULE_1__.vShow,S.value.tabs.length]]),(0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(n).search?((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"button\",{key:0,ref:\"gif-button\",type:\"button\",class:(0,vue__WEBPACK_IMPORTED_MODULE_1__.normalizeClass)([\"wl-action\",{active:K.value}]),title:b.value.gif,onClick:r[2]||(r[2]=k=>K.value=!K.value)},[(0,vue__WEBPACK_IMPORTED_MODULE_1__.createVNode)((0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(Na))],10,Il)):(0,vue__WEBPACK_IMPORTED_MODULE_1__.createCommentVNode)(\"v-if\",!0),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"input\",{id:\"wl-image-upload\",ref:\"image-uploader\",class:\"upload\",\"aria-hidden\":\"true\",type:\"file\",accept:\".png,.jpg,.jpeg,.webp,.bmp,.gif\",onChange:bt},null,544),q.value?((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"label\",{key:1,for:\"wl-image-upload\",class:\"wl-action\",title:b.value.uploadImage,\"aria-label\":b.value.uploadImage},[(0,vue__WEBPACK_IMPORTED_MODULE_1__.createVNode)((0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(Ua))],8,Al)):(0,vue__WEBPACK_IMPORTED_MODULE_1__.createCommentVNode)(\"v-if\",!0),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"button\",{type:\"button\",class:(0,vue__WEBPACK_IMPORTED_MODULE_1__.normalizeClass)([\"wl-action\",{active:X.value}]),title:b.value.preview,onClick:r[3]||(r[3]=k=>X.value=!X.value)},[(0,vue__WEBPACK_IMPORTED_MODULE_1__.createVNode)((0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(Va))],10,Ml)]),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"div\",xl,[r[9]||(r[9]=(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"div\",{class:\"wl-captcha-container\"},null,-1)),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"div\",Rl,[(0,vue__WEBPACK_IMPORTED_MODULE_1__.createTextVNode)((0,vue__WEBPACK_IMPORTED_MODULE_1__.toDisplayString)(V.value)+\" \",1),(0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(n).wordLimit?((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"span\",Sl,[r[8]||(r[8]=(0,vue__WEBPACK_IMPORTED_MODULE_1__.createTextVNode)(\"  /  \")),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"span\",{class:(0,vue__WEBPACK_IMPORTED_MODULE_1__.normalizeClass)({illegal:!Q.value}),textContent:(0,vue__WEBPACK_IMPORTED_MODULE_1__.toDisplayString)(le.value)},null,10,jl)])):(0,vue__WEBPACK_IMPORTED_MODULE_1__.createCommentVNode)(\"v-if\",!0),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createTextVNode)(\"  \"+(0,vue__WEBPACK_IMPORTED_MODULE_1__.toDisplayString)(b.value.word),1)]),(0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(n).login!==\"disable\"&&!F.value?((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"button\",{key:0,type:\"button\",class:\"wl-btn\",onClick:kt,textContent:(0,vue__WEBPACK_IMPORTED_MODULE_1__.toDisplayString)(b.value.login)},null,8,Ul)):(0,vue__WEBPACK_IMPORTED_MODULE_1__.createCommentVNode)(\"v-if\",!0),(0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(n).login!==\"force\"||F.value?((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"button\",{key:1,type:\"submit\",class:\"primary wl-btn\",title:\"Cmd|Ctrl + Enter\",disabled:w.value,onClick:Se},[w.value?((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createBlock)((0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(ue),{key:0,size:16})):((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(vue__WEBPACK_IMPORTED_MODULE_1__.Fragment,{key:1},[(0,vue__WEBPACK_IMPORTED_MODULE_1__.createTextVNode)((0,vue__WEBPACK_IMPORTED_MODULE_1__.toDisplayString)(b.value.submit),1)],64))],8,zl)):(0,vue__WEBPACK_IMPORTED_MODULE_1__.createCommentVNode)(\"v-if\",!0)]),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"div\",{ref:\"gif-popup\",class:(0,vue__WEBPACK_IMPORTED_MODULE_1__.normalizeClass)([\"wl-gif-popup\",{display:K.value}])},[(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"input\",{ref:\"gif-search\",type:\"text\",placeholder:b.value.gifSearchPlaceholder,onInput:r[4]||(r[4]=(...k)=>(0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(ze)&&(0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(ze)(...k))},null,40,Vl),T.list.length?((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createBlock)(cl,{key:0,items:T.list,\"column-width\":200,gap:6,onInsert:r[5]||(r[5]=k=>O(k)),onScroll:Ue},null,8,[\"items\"])):(0,vue__WEBPACK_IMPORTED_MODULE_1__.createCommentVNode)(\"v-if\",!0),T.loading?((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"div\",_l,[(0,vue__WEBPACK_IMPORTED_MODULE_1__.createVNode)((0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(ue),{size:30})])):(0,vue__WEBPACK_IMPORTED_MODULE_1__.createCommentVNode)(\"v-if\",!0)],2),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"div\",{ref:\"emoji-popup\",class:(0,vue__WEBPACK_IMPORTED_MODULE_1__.normalizeClass)([\"wl-emoji-popup\",{display:g.value}])},[((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(!0),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(vue__WEBPACK_IMPORTED_MODULE_1__.Fragment,null,(0,vue__WEBPACK_IMPORTED_MODULE_1__.renderList)(S.value.tabs,(k,D)=>((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(vue__WEBPACK_IMPORTED_MODULE_1__.Fragment,{key:k.name},[D===i.value?((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"div\",Hl,[((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(!0),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(vue__WEBPACK_IMPORTED_MODULE_1__.Fragment,null,(0,vue__WEBPACK_IMPORTED_MODULE_1__.renderList)(k.items,N=>((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"button\",{key:N,type:\"button\",title:N,onClick:Y=>O(`:${N}:`)},[g.value?((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"img\",{key:0,class:\"wl-emoji\",src:S.value.map[N],alt:N,loading:\"lazy\",referrerPolicy:\"no-referrer\"},null,8,Fl)):(0,vue__WEBPACK_IMPORTED_MODULE_1__.createCommentVNode)(\"v-if\",!0)],8,Tl))),128))])):(0,vue__WEBPACK_IMPORTED_MODULE_1__.createCommentVNode)(\"v-if\",!0)],64))),128)),S.value.tabs.length>1?((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"div\",Nl,[((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(!0),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(vue__WEBPACK_IMPORTED_MODULE_1__.Fragment,null,(0,vue__WEBPACK_IMPORTED_MODULE_1__.renderList)(S.value.tabs,(k,D)=>((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"button\",{key:k.name,type:\"button\",class:(0,vue__WEBPACK_IMPORTED_MODULE_1__.normalizeClass)([\"wl-tab\",{active:i.value===D}]),onClick:N=>i.value=D},[(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"img\",{class:\"wl-emoji\",src:k.icon,alt:k.name,title:k.name,loading:\"lazy\",referrerPolicy:\"no-referrer\"},null,8,Bl)],10,Wl))),128))])):(0,vue__WEBPACK_IMPORTED_MODULE_1__.createCommentVNode)(\"v-if\",!0)],2)])]),d.replyId||(H=d.edit)!=null&&H.objectId?((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"button\",{key:1,type:\"button\",class:\"wl-close\",title:b.value.cancelReply,onClick:r[6]||(r[6]=k=>d.replyId?a(\"cancelReply\"):a(\"cancelEdit\"))},[(0,vue__WEBPACK_IMPORTED_MODULE_1__.createVNode)((0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(vt),{size:24})],8,Dl)):(0,vue__WEBPACK_IMPORTED_MODULE_1__.createCommentVNode)(\"v-if\",!0)])}}});const Pl=[\"id\"],ql={class:\"wl-user\",\"aria-hidden\":\"true\"},Ol=[\"src\"],Gl={class:\"wl-card\"},Kl={class:\"wl-head\"},Zl=[\"href\"],Xl={key:1,class:\"wl-nick\"},Yl=[\"textContent\"],Jl=[\"textContent\"],Ql=[\"textContent\"],en=[\"textContent\"],tn=[\"textContent\"],an={class:\"wl-comment-actions\"},ln=[\"title\"],nn=[\"title\"],rn={class:\"wl-meta\",\"aria-hidden\":\"true\"},on=[\"data-value\",\"textContent\"],sn={key:0,class:\"wl-content\"},cn={key:0},un=[\"href\"],mn=[\"innerHTML\"],vn={key:1,class:\"wl-admin-actions\"},dn={class:\"wl-comment-status\"},pn=[\"disabled\",\"onClick\",\"textContent\"],gn={key:3,class:\"wl-quote\"};var hn=(0,vue__WEBPACK_IMPORTED_MODULE_1__.defineComponent)({__name:\"CommentCard\",props:{comment:{},edit:{},rootId:{},reply:{}},emits:[\"log\",\"submit\",\"delete\",\"like\",\"sticky\",\"edit\",\"reply\",\"status\"],setup(e,{emit:l}){const t=e,a=l,n=[\"approved\",\"waiting\",\"spam\"],s=(0,vue__WEBPACK_IMPORTED_MODULE_1__.inject)(we),h=dt(),c=(0,_vueuse_core__WEBPACK_IMPORTED_MODULE_6__.useNow)(),f=be(),v=(0,vue__WEBPACK_IMPORTED_MODULE_1__.computed)(()=>s.value.locale),y=(0,vue__WEBPACK_IMPORTED_MODULE_1__.computed)(()=>{const{link:i}=t.comment;return i?rt(i)?i:`https://${i}`:\"\"}),m=(0,vue__WEBPACK_IMPORTED_MODULE_1__.computed)(()=>h.value.includes(t.comment.objectId)),R=(0,vue__WEBPACK_IMPORTED_MODULE_1__.computed)(()=>pa(new Date(t.comment.time),c.value,v.value)),j=(0,vue__WEBPACK_IMPORTED_MODULE_1__.computed)(()=>f.value.type===\"administrator\"),E=(0,vue__WEBPACK_IMPORTED_MODULE_1__.computed)(()=>t.comment.user_id&&f.value.objectId===t.comment.user_id),$=(0,vue__WEBPACK_IMPORTED_MODULE_1__.computed)(()=>{var i;return t.comment.objectId===((i=t.reply)==null?void 0:i.objectId)}),S=(0,vue__WEBPACK_IMPORTED_MODULE_1__.computed)(()=>{var i;return t.comment.objectId===((i=t.edit)==null?void 0:i.objectId)});return(i,g)=>{var K;const X=(0,vue__WEBPACK_IMPORTED_MODULE_1__.resolveComponent)(\"CommentCard\",!0);return (0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"div\",{id:i.comment.objectId.toString(),class:\"wl-card-item\"},[(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"div\",ql,[i.comment.avatar?((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"img\",{key:0,class:\"wl-user-avatar\",src:i.comment.avatar,alt:\"\"},null,8,Ol)):(0,vue__WEBPACK_IMPORTED_MODULE_1__.createCommentVNode)(\"v-if\",!0),i.comment.type?((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createBlock)((0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(Fa),{key:1})):(0,vue__WEBPACK_IMPORTED_MODULE_1__.createCommentVNode)(\"v-if\",!0)]),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"div\",Gl,[(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"div\",Kl,[y.value?((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"a\",{key:0,class:\"wl-nick\",href:y.value,target:\"_blank\",rel:\"nofollow noopener noreferrer\"},(0,vue__WEBPACK_IMPORTED_MODULE_1__.toDisplayString)(i.comment.nick),9,Zl)):((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"span\",Xl,(0,vue__WEBPACK_IMPORTED_MODULE_1__.toDisplayString)(i.comment.nick),1)),i.comment.type===\"administrator\"?((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"span\",{key:2,class:\"wl-badge\",textContent:(0,vue__WEBPACK_IMPORTED_MODULE_1__.toDisplayString)(v.value.admin)},null,8,Yl)):(0,vue__WEBPACK_IMPORTED_MODULE_1__.createCommentVNode)(\"v-if\",!0),i.comment.label?((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"span\",{key:3,class:\"wl-badge\",textContent:(0,vue__WEBPACK_IMPORTED_MODULE_1__.toDisplayString)(i.comment.label)},null,8,Jl)):(0,vue__WEBPACK_IMPORTED_MODULE_1__.createCommentVNode)(\"v-if\",!0),i.comment.sticky?((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"span\",{key:4,class:\"wl-badge\",textContent:(0,vue__WEBPACK_IMPORTED_MODULE_1__.toDisplayString)(v.value.sticky)},null,8,Ql)):(0,vue__WEBPACK_IMPORTED_MODULE_1__.createCommentVNode)(\"v-if\",!0),typeof i.comment.level==\"number\"?((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"span\",{key:5,class:(0,vue__WEBPACK_IMPORTED_MODULE_1__.normalizeClass)(`wl-badge level${i.comment.level}`),textContent:(0,vue__WEBPACK_IMPORTED_MODULE_1__.toDisplayString)(v.value[`level${i.comment.level}`]||`Level ${i.comment.level}`)},null,10,en)):(0,vue__WEBPACK_IMPORTED_MODULE_1__.createCommentVNode)(\"v-if\",!0),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"span\",{class:\"wl-time\",textContent:(0,vue__WEBPACK_IMPORTED_MODULE_1__.toDisplayString)(R.value)},null,8,tn),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"div\",an,[j.value||E.value?((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(vue__WEBPACK_IMPORTED_MODULE_1__.Fragment,{key:0},[(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"button\",{type:\"button\",class:\"wl-edit\",onClick:g[0]||(g[0]=A=>a(\"edit\",i.comment))},[(0,vue__WEBPACK_IMPORTED_MODULE_1__.createVNode)((0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(Ta))]),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"button\",{type:\"button\",class:\"wl-delete\",onClick:g[1]||(g[1]=A=>a(\"delete\",i.comment))},[(0,vue__WEBPACK_IMPORTED_MODULE_1__.createVNode)((0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(Sa))])],64)):(0,vue__WEBPACK_IMPORTED_MODULE_1__.createCommentVNode)(\"v-if\",!0),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"button\",{type:\"button\",class:\"wl-like\",title:m.value?v.value.cancelLike:v.value.like,onClick:g[2]||(g[2]=A=>a(\"like\",i.comment))},[(0,vue__WEBPACK_IMPORTED_MODULE_1__.createVNode)((0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(za),{active:m.value},null,8,[\"active\"]),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createTextVNode)(\" \"+(0,vue__WEBPACK_IMPORTED_MODULE_1__.toDisplayString)(\"like\"in i.comment?i.comment.like:\"\"),1)],8,ln),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"button\",{type:\"button\",class:(0,vue__WEBPACK_IMPORTED_MODULE_1__.normalizeClass)([\"wl-reply\",{active:$.value}]),title:$.value?v.value.cancelReply:v.value.reply,onClick:g[3]||(g[3]=A=>a(\"reply\",$.value?null:i.comment))},[(0,vue__WEBPACK_IMPORTED_MODULE_1__.createVNode)((0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(Ha))],10,nn)])]),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"div\",rn,[((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(vue__WEBPACK_IMPORTED_MODULE_1__.Fragment,null,(0,vue__WEBPACK_IMPORTED_MODULE_1__.renderList)([\"addr\",\"browser\",\"os\"],A=>((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(vue__WEBPACK_IMPORTED_MODULE_1__.Fragment,null,[i.comment[A]?((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"span\",{key:A,class:(0,vue__WEBPACK_IMPORTED_MODULE_1__.normalizeClass)(`wl-${A}`),\"data-value\":i.comment[A],textContent:(0,vue__WEBPACK_IMPORTED_MODULE_1__.toDisplayString)(i.comment[A])},null,10,on)):(0,vue__WEBPACK_IMPORTED_MODULE_1__.createCommentVNode)(\"v-if\",!0)],64))),64))]),S.value?(0,vue__WEBPACK_IMPORTED_MODULE_1__.createCommentVNode)(\"v-if\",!0):((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"div\",sn,[\"reply_user\"in i.comment&&i.comment.reply_user?((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"p\",cn,[(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"a\",{href:\"#\"+i.comment.pid},\"@\"+(0,vue__WEBPACK_IMPORTED_MODULE_1__.toDisplayString)(i.comment.reply_user.nick),9,un),g[17]||(g[17]=(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"span\",null,\": \",-1))])):(0,vue__WEBPACK_IMPORTED_MODULE_1__.createCommentVNode)(\"v-if\",!0),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"div\",{innerHTML:i.comment.comment},null,8,mn)])),j.value&&!S.value?((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"div\",vn,[(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"span\",dn,[((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(vue__WEBPACK_IMPORTED_MODULE_1__.Fragment,null,(0,vue__WEBPACK_IMPORTED_MODULE_1__.renderList)(n,A=>(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"button\",{key:A,type:\"submit\",class:(0,vue__WEBPACK_IMPORTED_MODULE_1__.normalizeClass)(`wl-btn wl-${A}`),disabled:i.comment.status===A,onClick:V=>a(\"status\",{status:A,comment:i.comment}),textContent:(0,vue__WEBPACK_IMPORTED_MODULE_1__.toDisplayString)(v.value[A])},null,10,pn)),64))]),j.value&&!(\"rid\"in i.comment)?((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"button\",{key:0,type:\"submit\",class:\"wl-btn wl-sticky\",onClick:g[4]||(g[4]=A=>a(\"sticky\",i.comment))},(0,vue__WEBPACK_IMPORTED_MODULE_1__.toDisplayString)(i.comment.sticky?v.value.unsticky:v.value.sticky),1)):(0,vue__WEBPACK_IMPORTED_MODULE_1__.createCommentVNode)(\"v-if\",!0)])):(0,vue__WEBPACK_IMPORTED_MODULE_1__.createCommentVNode)(\"v-if\",!0),$.value||S.value?((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"div\",{key:2,class:(0,vue__WEBPACK_IMPORTED_MODULE_1__.normalizeClass)({\"wl-reply-wrapper\":$.value,\"wl-edit-wrapper\":S.value})},[(0,vue__WEBPACK_IMPORTED_MODULE_1__.createVNode)(gt,{edit:i.edit,\"reply-id\":(K=i.reply)==null?void 0:K.objectId,\"reply-user\":i.comment.nick,\"root-id\":i.rootId,onLog:g[5]||(g[5]=A=>a(\"log\")),onCancelReply:g[6]||(g[6]=A=>a(\"reply\",null)),onCancelEdit:g[7]||(g[7]=A=>a(\"edit\",null)),onSubmit:g[8]||(g[8]=A=>a(\"submit\",A))},null,8,[\"edit\",\"reply-id\",\"reply-user\",\"root-id\"])],2)):(0,vue__WEBPACK_IMPORTED_MODULE_1__.createCommentVNode)(\"v-if\",!0),\"children\"in i.comment?((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"div\",gn,[((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(!0),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(vue__WEBPACK_IMPORTED_MODULE_1__.Fragment,null,(0,vue__WEBPACK_IMPORTED_MODULE_1__.renderList)(i.comment.children,A=>((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createBlock)(X,{key:A.objectId,comment:A,reply:i.reply,edit:i.edit,\"root-id\":i.rootId,onLog:g[9]||(g[9]=V=>a(\"log\")),onDelete:g[10]||(g[10]=V=>a(\"delete\",V)),onEdit:g[11]||(g[11]=V=>a(\"edit\",V)),onLike:g[12]||(g[12]=V=>a(\"like\",V)),onReply:g[13]||(g[13]=V=>a(\"reply\",V)),onStatus:g[14]||(g[14]=V=>a(\"status\",V)),onSticky:g[15]||(g[15]=V=>a(\"sticky\",V)),onSubmit:g[16]||(g[16]=V=>a(\"submit\",V))},null,8,[\"comment\",\"reply\",\"edit\",\"root-id\"]))),128))])):(0,vue__WEBPACK_IMPORTED_MODULE_1__.createCommentVNode)(\"v-if\",!0)])],8,Pl)}}});const ht=\"3.6.0\",fn={\"data-waline\":\"\"},wn={class:\"wl-meta-head\"},yn={class:\"wl-count\"},bn=[\"textContent\"],kn={class:\"wl-sort\"},Cn=[\"onClick\"],$n={class:\"wl-cards\"},Ln={key:1,class:\"wl-operation\"},En=[\"textContent\"],In={key:2,class:\"wl-loading\"},An=[\"textContent\"],Mn={key:4,class:\"wl-operation\"},xn=[\"textContent\"],Rn={key:5,class:\"wl-power\"};var Sn=(0,vue__WEBPACK_IMPORTED_MODULE_1__.defineComponent)({__name:\"WalineComment\",props:{serverURL:{},path:{},meta:{},requiredMeta:{},wordLimit:{},pageSize:{},lang:{},locale:{},commentSorting:{},dark:{type:[String,Boolean]},login:{},noCopyright:{type:Boolean},recaptchaV3Key:{},turnstileKey:{},reaction:{type:[Array,Boolean]},emoji:{},search:{},highlighter:{type:Function},imageUploader:{type:Function},texRenderer:{type:Function}},setup(e){const l=e,t=be(),a=dt(),n=(0,vue__WEBPACK_IMPORTED_MODULE_1__.ref)(\"loading\"),s=(0,vue__WEBPACK_IMPORTED_MODULE_1__.ref)(0),h=(0,vue__WEBPACK_IMPORTED_MODULE_1__.ref)(1),c=(0,vue__WEBPACK_IMPORTED_MODULE_1__.ref)(0),f=(0,vue__WEBPACK_IMPORTED_MODULE_1__.computed)(()=>ma(l)),v=(0,vue__WEBPACK_IMPORTED_MODULE_1__.ref)(f.value.commentSorting),y=(0,vue__WEBPACK_IMPORTED_MODULE_1__.ref)([]),m=(0,vue__WEBPACK_IMPORTED_MODULE_1__.ref)(null),R=(0,vue__WEBPACK_IMPORTED_MODULE_1__.ref)(null),j=(0,vue__WEBPACK_IMPORTED_MODULE_1__.computed)(()=>va(f.value.dark)),E=(0,vue__WEBPACK_IMPORTED_MODULE_1__.computed)(()=>f.value.locale);(0,_vueuse_core__WEBPACK_IMPORTED_MODULE_6__.useStyleTag)(j,{id:\"waline-darkmode\"});let $=null;const S=w=>{const{serverURL:U,path:b,pageSize:F}=f.value,q=new AbortController;n.value=\"loading\",$==null||$(),(0,_waline_api__WEBPACK_IMPORTED_MODULE_0__.getComment)({serverURL:U,lang:f.value.lang,path:b,pageSize:F,sortBy:at[v.value],page:w,signal:q.signal,token:t.value.token}).then(O=>{n.value=\"success\",s.value=O.count,y.value.push(...O.data),h.value=w,c.value=O.totalPages}).catch(O=>{O.name!==\"AbortError\"&&(console.error(O.message),n.value=\"error\")}),$=q.abort.bind(q)},i=()=>{S(h.value+1)},g=()=>{s.value=0,y.value=[],S(1)},K=w=>{v.value!==w&&(v.value=w,g())},X=w=>{m.value=w},A=w=>{R.value=w},V=w=>{if(R.value)R.value.comment=w.comment,R.value.orig=w.orig;else if(\"rid\"in w){const U=y.value.find(({objectId:b})=>b===w.rid);if(!U)return;Array.isArray(U.children)||(U.children=[]),U.children.push(w)}else y.value.unshift(w),s.value+=1},T=async({comment:w,status:U})=>{if(w.status===U)return;const{serverURL:b,lang:F}=f.value;await (0,_waline_api__WEBPACK_IMPORTED_MODULE_0__.updateComment)({serverURL:b,lang:F,token:t.value.token,objectId:w.objectId,comment:{status:U}}),w.status=U},le=async w=>{if(\"rid\"in w)return;const{serverURL:U,lang:b}=f.value;await (0,_waline_api__WEBPACK_IMPORTED_MODULE_0__.updateComment)({serverURL:U,lang:b,token:t.value.token,objectId:w.objectId,comment:{sticky:w.sticky?0:1}}),w.sticky=!w.sticky},Q=async({objectId:w})=>{if(!confirm(\"Are you sure you want to delete this comment?\"))return;const{serverURL:U,lang:b}=f.value;await (0,_waline_api__WEBPACK_IMPORTED_MODULE_0__.deleteComment)({serverURL:U,lang:b,token:t.value.token,objectId:w}),y.value.some((F,q)=>F.objectId===w?(y.value=y.value.filter((O,oe)=>oe!==q),!0):F.children.some((O,oe)=>O.objectId===w?(y.value[q].children=F.children.filter((ve,ke)=>ke!==oe),!0):!1))},me=async w=>{const{serverURL:U,lang:b}=f.value,{objectId:F}=w,q=a.value.includes(F);await (0,_waline_api__WEBPACK_IMPORTED_MODULE_0__.updateComment)({serverURL:U,lang:b,objectId:F,token:t.value.token,comment:{like:!q}}),q?a.value=a.value.filter(O=>O!==F):(a.value=[...a.value,F],a.value.length>50&&(a.value=a.value.slice(-50))),w.like=Math.max(0,(w.like||0)+(q?-1:1))};return (0,vue__WEBPACK_IMPORTED_MODULE_1__.provide)(we,f),(0,vue__WEBPACK_IMPORTED_MODULE_1__.onMounted)(()=>{(0,_vueuse_core__WEBPACK_IMPORTED_MODULE_7__.watchImmediate)(()=>[l.serverURL,l.path],()=>{g()})}),(0,vue__WEBPACK_IMPORTED_MODULE_1__.onUnmounted)(()=>{$==null||$()}),(w,U)=>((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"div\",fn,[(0,vue__WEBPACK_IMPORTED_MODULE_1__.createVNode)(ol),!m.value&&!R.value?((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createBlock)(gt,{key:0,onLog:g,onSubmit:V})):(0,vue__WEBPACK_IMPORTED_MODULE_1__.createCommentVNode)(\"v-if\",!0),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"div\",wn,[(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"div\",yn,[s.value?((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"span\",{key:0,class:\"wl-num\",textContent:(0,vue__WEBPACK_IMPORTED_MODULE_1__.toDisplayString)(s.value)},null,8,bn)):(0,vue__WEBPACK_IMPORTED_MODULE_1__.createCommentVNode)(\"v-if\",!0),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createTextVNode)(\" \"+(0,vue__WEBPACK_IMPORTED_MODULE_1__.toDisplayString)(E.value.comment),1)]),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"ul\",kn,[((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(!0),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(vue__WEBPACK_IMPORTED_MODULE_1__.Fragment,null,(0,vue__WEBPACK_IMPORTED_MODULE_1__.renderList)((0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(ca),b=>((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"li\",{key:b,class:(0,vue__WEBPACK_IMPORTED_MODULE_1__.normalizeClass)([b===v.value?\"active\":\"\"]),onClick:F=>K(b)},(0,vue__WEBPACK_IMPORTED_MODULE_1__.toDisplayString)(E.value[b]),11,Cn))),128))])]),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"div\",$n,[((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(!0),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(vue__WEBPACK_IMPORTED_MODULE_1__.Fragment,null,(0,vue__WEBPACK_IMPORTED_MODULE_1__.renderList)(y.value,b=>((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createBlock)(hn,{key:b.objectId,\"root-id\":b.objectId,comment:b,reply:m.value,edit:R.value,onLog:g,onReply:X,onEdit:A,onSubmit:V,onStatus:T,onDelete:Q,onSticky:le,onLike:me},null,8,[\"root-id\",\"comment\",\"reply\",\"edit\"]))),128))]),n.value===\"error\"?((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"div\",Ln,[(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"button\",{type:\"button\",class:\"wl-btn\",onClick:g,textContent:(0,vue__WEBPACK_IMPORTED_MODULE_1__.toDisplayString)(E.value.refresh)},null,8,En)])):n.value===\"loading\"?((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"div\",In,[(0,vue__WEBPACK_IMPORTED_MODULE_1__.createVNode)((0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(ue),{size:30})])):y.value.length?h.value<c.value?((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"div\",Mn,[(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"button\",{type:\"button\",class:\"wl-btn\",onClick:i,textContent:(0,vue__WEBPACK_IMPORTED_MODULE_1__.toDisplayString)(E.value.more)},null,8,xn)])):(0,vue__WEBPACK_IMPORTED_MODULE_1__.createCommentVNode)(\"v-if\",!0):((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"div\",{key:3,class:\"wl-empty\",textContent:(0,vue__WEBPACK_IMPORTED_MODULE_1__.toDisplayString)(E.value.sofa)},null,8,An)),f.value.noCopyright?(0,vue__WEBPACK_IMPORTED_MODULE_1__.createCommentVNode)(\"v-if\",!0):((0,vue__WEBPACK_IMPORTED_MODULE_1__.openBlock)(),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementBlock)(\"div\",Rn,[U[0]||(U[0]=(0,vue__WEBPACK_IMPORTED_MODULE_1__.createTextVNode)(\" Powered by \")),U[1]||(U[1]=(0,vue__WEBPACK_IMPORTED_MODULE_1__.createElementVNode)(\"a\",{href:\"https://github.com/walinejs/waline\",target:\"_blank\",rel:\"noopener noreferrer\"},\" Waline \",-1)),(0,vue__WEBPACK_IMPORTED_MODULE_1__.createTextVNode)(\" v\"+(0,vue__WEBPACK_IMPORTED_MODULE_1__.toDisplayString)((0,vue__WEBPACK_IMPORTED_MODULE_1__.unref)(ht)),1)]))]))}});const ft=(e,l)=>{l.forEach((t,a)=>{const n=e[a].time;typeof n==\"number\"&&(t.innerText=n.toString())})},wt=({serverURL:e,path:l=window.location.pathname,selector:t=\".waline-pageview-count\",update:a=!0,lang:n=navigator.language})=>{const s=new AbortController,h=Array.from(document.querySelectorAll(t)),c=v=>{const y=Re(v);return y!==null&&l!==y},f=v=>(0,_waline_api__WEBPACK_IMPORTED_MODULE_0__.getPageview)({serverURL:ye(e),paths:v.map(y=>Re(y)??l),lang:n,signal:s.signal}).then(y=>ft(y,v)).catch(st);if(a){const v=h.filter(m=>!c(m)),y=h.filter(c);(0,_waline_api__WEBPACK_IMPORTED_MODULE_0__.updatePageview)({serverURL:ye(e),path:l,lang:n}).then(m=>ft(m,v)),y.length&&f(y)}else f(h);return s.abort.bind(s)},jn=({el:e=\"#waline\",path:l=window.location.pathname,comment:t=!1,pageview:a=!1,...n})=>{const s=e?xe(e):null;if(e&&!s)throw new Error(\"Option 'el' do not match any domElement!\");if(!n.serverURL)throw new Error(\"Option 'serverURL' is missing!\");const h=(0,vue__WEBPACK_IMPORTED_MODULE_1__.reactive)({...n}),c=(0,vue__WEBPACK_IMPORTED_MODULE_1__.reactive)({comment:t,pageview:a,path:l}),f=()=>{c.comment&&mt({serverURL:h.serverURL,path:c.path,...re(c.comment)?{selector:c.comment}:{}})},v=()=>{c.pageview&&wt({serverURL:h.serverURL,path:c.path,...re(c.pageview)?{selector:c.pageview}:{}})};let y=null;s&&(y=(0,vue__WEBPACK_IMPORTED_MODULE_1__.createApp)(()=>(0,vue__WEBPACK_IMPORTED_MODULE_1__.h)(Sn,{path:c.path,...h})),y.mount(s));const m=(0,vue__WEBPACK_IMPORTED_MODULE_1__.watchEffect)(f),R=(0,vue__WEBPACK_IMPORTED_MODULE_1__.watchEffect)(v);return{el:s,update:({comment:j,pageview:E,path:$=window.location.pathname,...S}={})=>{Object.entries(S).forEach(([i,g])=>{h[i]=g}),c.path=$,j!==void 0&&(c.comment=j),E!==void 0&&(c.pageview=E)},destroy:()=>{y==null||y.unmount(),m(),R()}}},Un=({el:e,serverURL:l,count:t,lang:a=navigator.language})=>{const n=be(),s=xe(e),h=new AbortController;return (0,_waline_api__WEBPACK_IMPORTED_MODULE_0__.getRecentComment)({serverURL:l,count:t,lang:a,signal:h.signal,token:n.value.token}).then(c=>s&&c.length?(s.innerHTML=`<ul class=\"wl-recent-list\">${c.map(f=>`<li class=\"wl-recent-item\"><a href=\"${f.url}\">${f.nick}</a>：${f.comment}</li>`).join(\"\")}</ul>`,{comments:c,destroy:()=>{h.abort(),s.innerHTML=\"\"}}):{comments:c,destroy:()=>h.abort()})},zn=({el:e,serverURL:l,count:t,locale:a,lang:n=navigator.language,mode:s=\"list\"})=>{const h=xe(e),c=new AbortController;return (0,_waline_api__WEBPACK_IMPORTED_MODULE_0__.getUserList)({serverURL:l,pageSize:t,lang:n,signal:c.signal}).then(f=>!h||!f.length?{users:f,destroy:()=>c.abort()}:(a={...et(n),...typeof a==\"object\"?a:{}},h.innerHTML=`<ul class=\"wl-user-${s}\">${f.map((v,y)=>[`<li class=\"wl-user-item\" aria-label=\"${v.nick}\">`,v.link&&`<a href=\"${v.link}\" target=\"_blank\">`,'<div class=\"wl-user-avatar\">',`<img src=\"${v.avatar}\" alt=\"${v.nick}\">`,`<span class=\"wl-user-badge\">${y+1}</span>`,\"</div>\",'<div class=\"wl-user-meta\">','<div class=\"wl-user-name\">',v.nick,v.level&&`<span class=\"wl-badge\">${a?a[`level${v.level}`]:`Level ${v.level}`}</span>`,v.label&&`<span class=\"wl-badge\">${v.label}</span>`,\"</div>\",v.link&&v.link,\"</div>\",v.link&&\"</a>\",\"</li>\"].filter(m=>m).join(\"\")).join(\"\")}</ul>`,{users:f,destroy:()=>{c.abort(),h.innerHTML=\"\"}}))};\n//# sourceMappingURL=slim.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@waline/client/dist/slim.js\n");

/***/ })

};
;