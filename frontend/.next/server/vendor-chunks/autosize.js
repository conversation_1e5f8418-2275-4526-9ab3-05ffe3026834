"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/autosize";
exports.ids = ["vendor-chunks/autosize"];
exports.modules = {

/***/ "(ssr)/./node_modules/autosize/dist/autosize.esm.js":
/*!****************************************************!*\
  !*** ./node_modules/autosize/dist/autosize.esm.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nvar e=new Map;function t(t){var o=e.get(t);o&&o.destroy()}function o(t){var o=e.get(t);o&&o.update()}var r=null;\"undefined\"==typeof window?((r=function(e){return e}).destroy=function(e){return e},r.update=function(e){return e}):((r=function(t,o){return t&&Array.prototype.forEach.call(t.length?t:[t],function(t){return function(t){if(t&&t.nodeName&&\"TEXTAREA\"===t.nodeName&&!e.has(t)){var o,r=null,n=window.getComputedStyle(t),i=(o=t.value,function(){a({testForHeightReduction:\"\"===o||!t.value.startsWith(o),restoreTextAlign:null}),o=t.value}),l=function(o){t.removeEventListener(\"autosize:destroy\",l),t.removeEventListener(\"autosize:update\",s),t.removeEventListener(\"input\",i),window.removeEventListener(\"resize\",s),Object.keys(o).forEach(function(e){return t.style[e]=o[e]}),e.delete(t)}.bind(t,{height:t.style.height,resize:t.style.resize,textAlign:t.style.textAlign,overflowY:t.style.overflowY,overflowX:t.style.overflowX,wordWrap:t.style.wordWrap});t.addEventListener(\"autosize:destroy\",l),t.addEventListener(\"autosize:update\",s),t.addEventListener(\"input\",i),window.addEventListener(\"resize\",s),t.style.overflowX=\"hidden\",t.style.wordWrap=\"break-word\",e.set(t,{destroy:l,update:s}),s()}function a(e){var o,i,l=e.restoreTextAlign,s=void 0===l?null:l,d=e.testForHeightReduction,u=void 0===d||d,c=n.overflowY;if(0!==t.scrollHeight&&(\"vertical\"===n.resize?t.style.resize=\"none\":\"both\"===n.resize&&(t.style.resize=\"horizontal\"),u&&(o=function(e){for(var t=[];e&&e.parentNode&&e.parentNode instanceof Element;)e.parentNode.scrollTop&&t.push([e.parentNode,e.parentNode.scrollTop]),e=e.parentNode;return function(){return t.forEach(function(e){var t=e[0],o=e[1];t.style.scrollBehavior=\"auto\",t.scrollTop=o,t.style.scrollBehavior=null})}}(t),t.style.height=\"\"),i=\"content-box\"===n.boxSizing?t.scrollHeight-(parseFloat(n.paddingTop)+parseFloat(n.paddingBottom)):t.scrollHeight+parseFloat(n.borderTopWidth)+parseFloat(n.borderBottomWidth),\"none\"!==n.maxHeight&&i>parseFloat(n.maxHeight)?(\"hidden\"===n.overflowY&&(t.style.overflow=\"scroll\"),i=parseFloat(n.maxHeight)):\"hidden\"!==n.overflowY&&(t.style.overflow=\"hidden\"),t.style.height=i+\"px\",s&&(t.style.textAlign=s),o&&o(),r!==i&&(t.dispatchEvent(new Event(\"autosize:resized\",{bubbles:!0})),r=i),c!==n.overflow&&!s)){var v=n.textAlign;\"hidden\"===n.overflow&&(t.style.textAlign=\"start\"===v?\"end\":\"start\"),a({restoreTextAlign:v,testForHeightReduction:!0})}}function s(){a({testForHeightReduction:!0,restoreTextAlign:null})}}(t)}),t}).destroy=function(e){return e&&Array.prototype.forEach.call(e.length?e:[e],t),e},r.update=function(e){return e&&Array.prototype.forEach.call(e.length?e:[e],o),e});var n=r;/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (n);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/autosize/dist/autosize.esm.js\n");

/***/ })

};
;