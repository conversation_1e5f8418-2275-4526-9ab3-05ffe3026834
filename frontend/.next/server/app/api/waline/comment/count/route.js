"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/waline/comment/count/route";
exports.ids = ["app/api/waline/comment/count/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fwaline%2Fcomment%2Fcount%2Froute&page=%2Fapi%2Fwaline%2Fcomment%2Fcount%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwaline%2Fcomment%2Fcount%2Froute.ts&appDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend%2Fsrc%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=mdx&rootDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fwaline%2Fcomment%2Fcount%2Froute&page=%2Fapi%2Fwaline%2Fcomment%2Fcount%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwaline%2Fcomment%2Fcount%2Froute.ts&appDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend%2Fsrc%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=mdx&rootDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_yao_Code_me_My_web_frontend_src_app_api_waline_comment_count_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/waline/comment/count/route.ts */ \"(rsc)/./src/app/api/waline/comment/count/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/waline/comment/count/route\",\n        pathname: \"/api/waline/comment/count\",\n        filename: \"route\",\n        bundlePath: \"app/api/waline/comment/count/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/Code/me/My-web/frontend/src/app/api/waline/comment/count/route.ts\",\n    nextConfigOutput,\n    userland: _home_yao_Code_me_My_web_frontend_src_app_api_waline_comment_count_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/waline/comment/count/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fwaline%2Fcomment%2Fcount%2Froute&page=%2Fapi%2Fwaline%2Fcomment%2Fcount%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwaline%2Fcomment%2Fcount%2Froute.ts&appDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend%2Fsrc%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=mdx&rootDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/waline/comment/count/route.ts":
/*!***************************************************!*\
  !*** ./src/app/api/waline/comment/count/route.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_walineConfig__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/walineConfig */ \"(rsc)/./src/lib/walineConfig.ts\");\n\n\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const path = searchParams.get(\"path\");\n        const paths = searchParams.getAll(\"paths\") // 支持批量查询\n        ;\n        if (!path && paths.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Path parameter is required\"\n            }, {\n                status: 400\n            });\n        }\n        // 获取Waline配置\n        const config = await (0,_lib_walineConfig__WEBPACK_IMPORTED_MODULE_1__.getWalineConfig)();\n        // 准备路径数组\n        const pathsToQuery = paths.length > 0 ? paths : [\n            path\n        ];\n        // 直接调用Waline API获取评论数量\n        const walineApiUrl = `${config.serverURL}/api/comment`;\n        const params = new URLSearchParams({\n            type: \"count\",\n            url: pathsToQuery.join(\",\")\n        });\n        const response = await fetch(`${walineApiUrl}?${params}`, {\n            headers: {\n                \"Accept\": \"application/json\"\n            }\n        });\n        if (!response.ok) {\n            throw new Error(`Waline API error: ${response.status}`);\n        }\n        const commentCounts = await response.json();\n        // 如果只查询单个路径，返回单个数值\n        if (path && paths.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: commentCounts[0] || 0,\n                path\n            });\n        }\n        // 批量查询返回数组\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: commentCounts,\n            paths: pathsToQuery\n        });\n    } catch (error) {\n        console.error(\"Waline comment count API error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch comment count\",\n            details: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/waline/comment/count/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/walineConfig.ts":
/*!*********************************!*\
  !*** ./src/lib/walineConfig.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearWalineConfigCache: () => (/* binding */ clearWalineConfigCache),\n/* harmony export */   getWalineConfig: () => (/* binding */ getWalineConfig),\n/* harmony export */   getWalineConfigCacheStatus: () => (/* binding */ getWalineConfigCacheStatus),\n/* harmony export */   getWalineLang: () => (/* binding */ getWalineLang),\n/* harmony export */   getWalineServerURL: () => (/* binding */ getWalineServerURL),\n/* harmony export */   preloadWalineConfig: () => (/* binding */ preloadWalineConfig)\n/* harmony export */ });\n// 增强的缓存配置管理\nlet cachedConfig = null;\nlet configPromise = null;\nlet lastFetchTime = 0;\nconst CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存\nconst MAX_RETRIES = 3;\nconst RETRY_DELAY = 1000; // 1秒基础延迟\n/**\n * 延迟函数\n */ function delay(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\n/**\n * 带重试机制的Waline配置获取\n */ async function fetchWalineConfigWithRetry(retryCount = 0) {\n    try {\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), 5000); // 5秒超时\n        const response = await fetch(\"/api/system-config/waline/config\", {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            signal: controller.signal,\n            // 添加缓存策略\n            cache: \"default\"\n        });\n        clearTimeout(timeoutId);\n        if (!response.ok) {\n            throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const data = await response.json();\n        const config = {\n            serverURL: data.server_url,\n            lang: data.lang || \"zh-CN\"\n        };\n        // 验证配置有效性\n        if (!config.serverURL || config.serverURL.trim() === \"\") {\n            throw new Error(\"Invalid server URL received\");\n        }\n        return config;\n    } catch (error) {\n        // 如果还有重试次数，进行重试\n        if (retryCount < MAX_RETRIES) {\n            const delayMs = RETRY_DELAY * Math.pow(2, retryCount); // 指数退避\n            if ( true && process.env.ENABLE_WALINE_DEBUG === \"true\") {\n                console.warn(`Waline配置获取失败，${delayMs}ms后重试 (${retryCount + 1}/${MAX_RETRIES}):`, error);\n            }\n            await delay(delayMs);\n            return fetchWalineConfigWithRetry(retryCount + 1);\n        }\n        // 所有重试都失败了，记录错误并返回默认配置\n        if ( true && process.env.ENABLE_WALINE_DEBUG === \"true\") {\n            console.error(\"获取Waline配置彻底失败，使用默认配置:\", error);\n        }\n        // 返回默认配置作为后备\n        return {\n            serverURL: \"https://waline.jyaochen.cn\",\n            lang: \"zh-CN\"\n        };\n    }\n}\n/**\n * 从后台API获取Waline配置（兼容老版本）\n */ async function fetchWalineConfig() {\n    return fetchWalineConfigWithRetry();\n}\n/**\n * 获取Waline配置（带缓存）\n */ async function getWalineConfig() {\n    const now = Date.now();\n    // 检查缓存是否还有效\n    if (cachedConfig && now - lastFetchTime < CACHE_DURATION) {\n        return cachedConfig;\n    }\n    // 如果正在请求中，等待请求完成\n    if (configPromise) {\n        return configPromise;\n    }\n    // 发起新的请求\n    configPromise = fetchWalineConfigWithRetry();\n    try {\n        const config = await configPromise;\n        cachedConfig = config;\n        lastFetchTime = now;\n        if ( true && process.env.ENABLE_WALINE_DEBUG === \"true\") {\n            console.log(\"Waline配置获取成功:\", config);\n        }\n        return config;\n    } catch (error) {\n        // 请求失败，但不清除旧缓存（如果有的话）\n        if (cachedConfig) {\n            if ( true && process.env.ENABLE_WALINE_DEBUG === \"true\") {\n                console.warn(\"配置获取失败，使用缓存的配置:\", cachedConfig);\n            }\n            return cachedConfig;\n        }\n        // 没有缓存配置，抛出错误\n        throw error;\n    } finally{\n        // 请求完成，清除promise缓存\n        configPromise = null;\n    }\n}\n/**\n * 清除配置缓存（用于配置更新后刷新）\n */ function clearWalineConfigCache() {\n    cachedConfig = null;\n    configPromise = null;\n    lastFetchTime = 0;\n    if ( true && process.env.ENABLE_WALINE_DEBUG === \"true\") {\n        console.log(\"Waline配置缓存已清除\");\n    }\n}\n/**\n * 预加载Waline配置\n * 在用户可能需要评论功能之前预先获取配置\n */ async function preloadWalineConfig() {\n    try {\n        await getWalineConfig();\n    } catch (error) {\n        // 预加载失败不影响主流程\n        if ( true && process.env.ENABLE_WALINE_DEBUG === \"true\") {\n            console.warn(\"Waline配置预加载失败:\", error);\n        }\n    }\n}\n/**\n * 获取配置缓存状态\n */ function getWalineConfigCacheStatus() {\n    const now = Date.now();\n    const cacheAge = now - lastFetchTime;\n    return {\n        isCached: !!cachedConfig,\n        isExpired: cacheAge > CACHE_DURATION,\n        lastFetchTime,\n        cacheAge\n    };\n}\n/**\n * 获取Waline服务器URL\n */ async function getWalineServerURL() {\n    const config = await getWalineConfig();\n    return config.serverURL;\n}\n/**\n * 获取Waline语言设置\n */ async function getWalineLang() {\n    const config = await getWalineConfig();\n    return config.lang;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/walineConfig.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fwaline%2Fcomment%2Fcount%2Froute&page=%2Fapi%2Fwaline%2Fcomment%2Fcount%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwaline%2Fcomment%2Fcount%2Froute.ts&appDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend%2Fsrc%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=mdx&rootDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();