"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/system-config/waline/config/route";
exports.ids = ["app/api/system-config/waline/config/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsystem-config%2Fwaline%2Fconfig%2Froute&page=%2Fapi%2Fsystem-config%2Fwaline%2Fconfig%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsystem-config%2Fwaline%2Fconfig%2Froute.ts&appDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend%2Fsrc%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=mdx&rootDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsystem-config%2Fwaline%2Fconfig%2Froute&page=%2Fapi%2Fsystem-config%2Fwaline%2Fconfig%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsystem-config%2Fwaline%2Fconfig%2Froute.ts&appDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend%2Fsrc%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=mdx&rootDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_yao_Code_me_My_web_frontend_src_app_api_system_config_waline_config_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/system-config/waline/config/route.ts */ \"(rsc)/./src/app/api/system-config/waline/config/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/system-config/waline/config/route\",\n        pathname: \"/api/system-config/waline/config\",\n        filename: \"route\",\n        bundlePath: \"app/api/system-config/waline/config/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/Code/me/My-web/frontend/src/app/api/system-config/waline/config/route.ts\",\n    nextConfigOutput,\n    userland: _home_yao_Code_me_My_web_frontend_src_app_api_system_config_waline_config_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/system-config/waline/config/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhcGklMkZzeXN0ZW0tY29uZmlnJTJGd2FsaW5lJTJGY29uZmlnJTJGcm91dGUmcGFnZT0lMkZhcGklMkZzeXN0ZW0tY29uZmlnJTJGd2FsaW5lJTJGY29uZmlnJTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGc3lzdGVtLWNvbmZpZyUyRndhbGluZSUyRmNvbmZpZyUyRnJvdXRlLnRzJmFwcERpcj0lMkZob21lJTJGeWFvJTJGQ29kZSUyRm1lJTJGTXktd2ViJTJGZnJvbnRlbmQlMkZzcmMlMkZhcHAmcGFnZUV4dGVuc2lvbnM9anMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz1tZHgmcm9vdERpcj0lMkZob21lJTJGeWFvJTJGQ29kZSUyRm1lJTJGTXktd2ViJTJGZnJvbnRlbmQmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9c3RhbmRhbG9uZSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBc0c7QUFDdkM7QUFDYztBQUNrQztBQUMvRztBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsZ0hBQW1CO0FBQzNDO0FBQ0EsY0FBYyx5RUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLGlFQUFpRTtBQUN6RTtBQUNBO0FBQ0EsV0FBVyw0RUFBVztBQUN0QjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ3VIOztBQUV2SCIsInNvdXJjZXMiOlsid2VicGFjazovL2NvcmV5Y2hpdS1wb3J0Zm9saW8tdGVtcGxhdGUvP2JiOGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL2FwcC1yb3V0ZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiL2hvbWUveWFvL0NvZGUvbWUvTXktd2ViL2Zyb250ZW5kL3NyYy9hcHAvYXBpL3N5c3RlbS1jb25maWcvd2FsaW5lL2NvbmZpZy9yb3V0ZS50c1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJzdGFuZGFsb25lXCJcbmNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFJvdXRlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9ST1VURSxcbiAgICAgICAgcGFnZTogXCIvYXBpL3N5c3RlbS1jb25maWcvd2FsaW5lL2NvbmZpZy9yb3V0ZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL3N5c3RlbS1jb25maWcvd2FsaW5lL2NvbmZpZ1wiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvc3lzdGVtLWNvbmZpZy93YWxpbmUvY29uZmlnL3JvdXRlXCJcbiAgICB9LFxuICAgIHJlc29sdmVkUGFnZVBhdGg6IFwiL2hvbWUveWFvL0NvZGUvbWUvTXktd2ViL2Zyb250ZW5kL3NyYy9hcHAvYXBpL3N5c3RlbS1jb25maWcvd2FsaW5lL2NvbmZpZy9yb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHJlcXVlc3RBc3luY1N0b3JhZ2UsIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmNvbnN0IG9yaWdpbmFsUGF0aG5hbWUgPSBcIi9hcGkvc3lzdGVtLWNvbmZpZy93YWxpbmUvY29uZmlnL3JvdXRlXCI7XG5mdW5jdGlvbiBwYXRjaEZldGNoKCkge1xuICAgIHJldHVybiBfcGF0Y2hGZXRjaCh7XG4gICAgICAgIHNlcnZlckhvb2tzLFxuICAgICAgICBzdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlXG4gICAgfSk7XG59XG5leHBvcnQgeyByb3V0ZU1vZHVsZSwgcmVxdWVzdEFzeW5jU3RvcmFnZSwgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIG9yaWdpbmFsUGF0aG5hbWUsIHBhdGNoRmV0Y2gsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsystem-config%2Fwaline%2Fconfig%2Froute&page=%2Fapi%2Fsystem-config%2Fwaline%2Fconfig%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsystem-config%2Fwaline%2Fconfig%2Froute.ts&appDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend%2Fsrc%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=mdx&rootDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/system-config/waline/config/route.ts":
/*!**********************************************************!*\
  !*** ./src/app/api/system-config/waline/config/route.ts ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nconst BACKEND_URL = process.env.BACKEND_URL || \"http://localhost:8000\";\nasync function GET(request) {\n    try {\n        // 从后台API获取Waline配置\n        const response = await fetch(`${BACKEND_URL}/api/system-config/waline/config`, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            // 添加缓存控制，避免频繁请求\n            next: {\n                revalidate: 300\n            } // 5分钟缓存\n        });\n        if (!response.ok) {\n            throw new Error(`Backend API error: ${response.status} ${response.statusText}`);\n        }\n        const data = await response.json();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            server_url: data.server_url || \"https://waline.jyaochen.cn\",\n            lang: data.lang || \"zh-CN\"\n        });\n    } catch (error) {\n        console.error(\"获取Waline配置失败:\", error);\n        // 返回默认配置作为后备\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            server_url: \"https://waline.jyaochen.cn\",\n            lang: \"zh-CN\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/system-config/waline/config/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsystem-config%2Fwaline%2Fconfig%2Froute&page=%2Fapi%2Fsystem-config%2Fwaline%2Fconfig%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsystem-config%2Fwaline%2Fconfig%2Froute.ts&appDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend%2Fsrc%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=mdx&rootDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();