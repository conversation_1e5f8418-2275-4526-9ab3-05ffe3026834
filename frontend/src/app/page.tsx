import { Container } from '@/components/layout/Container'
import Newsletter from '@/components/home/<USER>'
import Feed from '@/components/home/<USER>'
import Career from '@/components/home/<USER>'
import Education from '@/components/home/<USER>'
import SocialLinksWrapper from '@/components/home/<USER>'
import { BlogCard } from '@/components/home/<USER>'
import { getHomepageContent, type BlogType } from '@/lib/blogs'
import { ProjectCard } from '@/components/project/ProjectCard'
import { getPersonalInfo, getHomepageSections, getLayoutData } from '@/lib/api'
import { type ProjectItemType } from '@/lib/projects'
import GithubContributions from '@/components/home/<USER>'

// ISR 配置 - 增量静态再生成
export const revalidate = process.env.NODE_ENV === 'development' ? 0 : 60 // 开发环境不缓存，生产环境1分钟重新验证
import { AnimatedSection, ScrollReveal } from '@/components/ui/page-transition'
import { GradientText, PatternBackground } from '@/components/ui/visual-enhancements'
import GitHubSnake from '@/components/home/<USER>'
import { CustomIcon } from '@/components/shared/CustomIcon'
// IconCloud 已移除，现在只在项目页使用
// import IconCloud from "@/components/ui/icon-cloud";
// import CompactGalleryPreview from "@/components/home/<USER>"; // 暂时注释掉
import { FileTextIcon } from 'lucide-react'
import { AntdIcon } from '@/components/shared/AntdIcon'




export default async function Home() {
  // 并行获取所有数据 - 性能优化，添加错误处理
  const [
    personalInfo,
    homepageSections,
    layoutData,
    homepageContent
  ] = await Promise.allSettled([
    getPersonalInfo().catch(error => {
      console.error('Failed to fetch personal info:', error);
      // 返回默认个人信息
      return {
        name: 'Jay-Yao',
        headline: 'Master\'s candidate in Information Science',
        introduction: 'I\'m focusing on applications of LLM and KG in Traditional Chinese Medicine. My research aims to develop AI-driven frameworks for TCM knowledge discovery and clinical decision support.'
      };
    }),
    getHomepageSections(),
    getLayoutData('homepage'),
    getHomepageContent() // 使用新的统一API
  ]).then(results => results.map(result => result.status === 'fulfilled' ? result.value : null));

  // 检查区块是否启用的辅助函数
  const isBlockEnabled = (blockId: string): boolean => {
    // 确保 layoutData 存在且有 blocks 属性
    if (layoutData && 'blocks' in layoutData && Array.isArray(layoutData.blocks)) {
      const block = layoutData.blocks.find(b => b.block_id === blockId);
      return block ? block.enabled : true;
    }
    return true; // 默认启用
  };

  // 从统一API获取的数据，添加默认值处理
  const blogList = (homepageContent && 'blogs' in homepageContent) ? homepageContent.blogs : [];
  const projectList = (homepageContent && 'projects' in homepageContent) ? homepageContent.projects : [];

  // 将项目数据转换为分类格式以保持兼容性
  const featuredProjectsByCategory = projectList.length > 0 ? [{
    categoryName: "Featured Projects",
    name: "Featured Projects",
    icon: "star",
    description: "Projects displayed on homepage",
    projects: projectList.map((project: any) => ({
      ...project,
      name: project.title,
      description: project.description,
      link: project.project_url ? { href: project.project_url } : null,
      has_detail_page: true, // 项目都有详情页
      show_on_homepage: true,
      // 添加项目特有字段
      github_url: project.github_url,
      demo_url: project.demo_url,
      logo_url: project.logo_url,
      icon: project.icon,
      project_status: project.project_status,
      is_github_project: project.is_github_project,
      featured: project.featured,
      display_order: project.display_order,
      homepage_order: project.homepage_order
    }))
  }] : [];

  return (
    <>
      <Container className="mt-6 mb-0">
        {/* personal info - hero block */}
        {isBlockEnabled('hero') && (
          <div className="relative mb-4 rounded-2xl overflow-hidden">
            {/* 增强的背景效果 */}
            <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5 rounded-2xl" />
            <div className="relative">
              {/* 动态光晕效果 */}
              <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-primary/10 rounded-full blur-3xl animate-pulse-soft" />
              <div className="absolute bottom-1/4 right-1/4 w-24 h-24 bg-secondary/10 rounded-full blur-2xl animate-pulse-soft" style={{ animationDelay: '1s' }} />

              <div className="relative p-8 py-16">
                <div className="max-w-4xl mx-auto text-center">
                  <AnimatedSection>
                    {/* 增强的标题效果 */}
                    <div className="relative mb-8">
                      <h2 className="text-3xl font-bold tracking-tight sm:text-4xl lg:text-5xl mb-4 relative">
                        <GradientText variant="primary" animate className="relative z-10">
                          {(personalInfo && 'headline' in personalInfo) ? personalInfo.headline : 'Master\'s candidate in Information Science'}
                        </GradientText>
                        {/* 文字光晕效果 */}
                        <div className="absolute inset-0 text-3xl font-bold tracking-tight sm:text-4xl lg:text-5xl text-primary/20 blur-sm -z-10">
                          {(personalInfo && 'headline' in personalInfo) ? personalInfo.headline : 'Master\'s candidate in Information Science'}
                        </div>
                      </h2>
                    </div>

                    {/* 增强的介绍文字 */}
                    <div className="relative mb-12">
                      <p className="text-lg text-muted-foreground leading-relaxed max-w-3xl mx-auto transition-all duration-300 hover:text-foreground/80">
                        {(personalInfo && 'introduction' in personalInfo) ? personalInfo.introduction : 'I\'m focusing on applications of LLM and KG in Traditional Chinese Medicine. My research aims to develop AI-driven frameworks for TCM knowledge discovery and clinical decision support.'}
                      </p>
                    </div>

                    {/* 增强的社交链接区域 */}
                    <div className="relative flex justify-center">
                      <SocialLinksWrapper className='animate-fade-in-up' style={{ animationDelay: '300ms' }}/>
                    </div>
                  </AnimatedSection>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 技术栈图标云区块已移除 - 现在只在项目页显示 */}

        {/* GitHub contributions block */}
        {isBlockEnabled('github-contributions') && (
          <div className="mt-0 border-t border-zinc-100 py-2 dark:border-zinc-700/40">
            {/* <GithubContributions /> */}
            <GitHubSnake />
          </div>
        )}

        {/* 渲染第一个分类 (if it exists) - featured-projects block */}
        {isBlockEnabled('featured-projects') && featuredProjectsByCategory.length > 0 && (
          <ScrollReveal className="mx-auto flex flex-col max-w-xl gap-3 lg:max-w-none mt-1 mb-2 pt-4 pb-5 border-t border-muted">
            <h2 className="text-3xl font-semibold tracking-tight md:text-5xl mb-2">
              <GradientText variant="ocean" animate>
                {(homepageSections && 'projectHeadLine' in homepageSections) ? homepageSections.projectHeadLine : 'My Projects'}
              </GradientText>
            </h2>
            <p className="text-base text-muted-foreground max-w-2xl mb-6 leading-relaxed">
              {(homepageSections && 'projectIntro' in homepageSections) ? homepageSections.projectIntro : 'Explore my collection of projects showcasing various technologies and skills.'}
            </p>
            {/* 显示第一个分类的精选项目 */}
            {featuredProjectsByCategory[0].projects.length > 0 && (
              <ul
                role="list"
                className="grid grid-cols-1 gap-x-8 gap-y-10 sm:grid-cols-2 md:grid-cols-3"
              >
                {featuredProjectsByCategory[0].projects.map((project, index) => (
                  <ProjectCard
                    key={project.name}
                    project={project}
                    titleAs='h3'
                    className="animate-fade-in-up"
                    style={{ animationDelay: `${index * 150}ms` }}
                  />
                ))}
              </ul>
            )}
          </ScrollReveal>
        )}

        {/* 渲染其他分类 (start from index 1) */}
        {featuredProjectsByCategory.slice(1).map(category => (
          // No need to check length again, already filtered
          <div key={category.categoryName} className="mx-auto flex flex-col max-w-xl gap-4 lg:max-w-none my-2 py-6 border-t border-muted">
            <h2 className="flex flex-row items-center justify-start gap-2 text-xl font-semibold tracking-tight md:text-3xl mb-4 group">
              {/* 使用分类图标 */}
              <span className="transition-transform duration-300 group-hover:scale-110 group-hover:text-primary">
                {category.icon ? (
                  <AntdIcon iconName={category.icon || ''} size={28} />
                ) : category.categoryName.toLowerCase().includes('open source') || category.categoryName.toLowerCase().includes('开源') ? (
                  <CustomIcon name='github' size={28}/>
                ) : (
                  <FileTextIcon size={28} />
                )}
              </span>
              {/* 使用分类名称 */}
              <GradientText variant="secondary" className="group-hover:text-primary transition-colors duration-300">
                {category.categoryName}
              </GradientText>
            </h2>
            {/* 显示分类描述 */}
            {(() => {
              if (category && category.description) {
                return (
                  <p className="text-base text-muted-foreground max-w-2xl mb-6">
                    {category.description}
                  </p>
                );
              }
              return null;
            })()}
            {/* 显示分类下的精选项目 */}
            <ul
              role="list"
              className="grid grid-cols-1 gap-x-8 gap-y-10 sm:grid-cols-2 md:grid-cols-3"
            >
              {category.projects.map((project) => (
                <ProjectCard key={project.name} project={project} titleAs='h3'/>
              ))}
            </ul>
          </div>
        ))}

        {/* recent-blogs block */}
        {isBlockEnabled('recent-blogs') && (
          <ScrollReveal className="mx-auto flex flex-col max-w-xl gap-4 py-6 my-6 lg:max-w-none border-t border-muted">
            <h2 className="text-3xl font-semibold tracking-tight md:text-5xl mb-2">
              <GradientText variant="ocean" animate>
                {(homepageSections && 'blogHeadLine' in homepageSections) ? homepageSections.blogHeadLine : 'Recent Blogs'}
              </GradientText>
            </h2>
            <p className="text-base text-muted-foreground max-w-2xl mb-6 leading-relaxed">
              {(homepageSections && 'blogIntro' in homepageSections) ? homepageSections.blogIntro : 'Latest thoughts and insights'}
            </p>
          </ScrollReveal>
        )}
        {isBlockEnabled('recent-blogs') && (
          <div className="mx-auto grid max-w-xl grid-cols-1 gap-y-14 lg:max-w-none lg:grid-cols-2">
            {/* left column */}
            {/* blog */}
            <div className="flex flex-col gap-12">
              {blogList.map((blog: BlogType, index: number) => (
                <div key={blog.slug} className="animate-fade-in-up" style={{ animationDelay: `${index * 150}ms` }}>
                  <BlogCard blog={blog} titleAs='h3'/>
                </div>
              ))}
            </div>

            {/* right column */}
            <div className="space-y-8 lg:pl-16 xl:pl-24">
              {isBlockEnabled('career') && (
                <AnimatedSection delay={300}>
                  <Career />
                </AnimatedSection>
              )}
              {isBlockEnabled('education') && (
                <AnimatedSection delay={400}>
                  <Education />
                </AnimatedSection>
              )}

              {/* <Newsletter /> */}
              {/* Subscribe my blogs 模块已注释 */}
              {/* {isBlockEnabled('feed') && (
                <AnimatedSection delay={600}>
                  <Feed />
                </AnimatedSection>
              )} */}
            </div>
          </div>
        )}
        {/* activity block - 暂时注释掉，留待以后更新使用 */}
        {/* {isBlockEnabled('activity') && (
          <ScrollReveal className="mx-auto flex flex-col max-w-xl gap-4 lg:max-w-none my-2 py-6 border-t border-muted">
            <h2 className="text-3xl font-semibold tracking-tight md:text-5xl mb-2">
              <GradientText variant="primary" animate>
                {homepageSections.activityHeadLine}
              </GradientText>
            </h2>
            <p className="text-base text-muted-foreground max-w-2xl mb-4">
              {homepageSections.activityIntro}
            </p>
            <CompactGalleryPreview />
          </ScrollReveal>
        )} */}
      </Container>
    </>
  )
}
