import { Footer } from '@/components/layout/Footer'
import { Header } from '@/components/layout/Header'
import { SmartPrefetch } from '@/hooks/useSmartPrefetch'

export function Layout({ children }: { children: React.ReactNode }) {
  // 默认路由用于预加载
  const routesToPrefetch = ['/', '/about', '/projects', '/blogs', '/gallery']

  return (
    <>
      {/* 智能预加载导航路由 */}
      <SmartPrefetch
        routes={routesToPrefetch}
        priority="low"
        delay={1500}
      />

      <div className="fixed inset-0 flex justify-center sm:px-8">
        <div className="flex w-full max-w-7xl lg:px-8">
          <div className="w-full shadow-xl dark:bg-muted" />
        </div>
      </div>
      <div className="relative flex w-full flex-col px-4 sm:px-0">
        <Header />
        <main className="flex-auto">{children}</main>
        <Footer />
      </div>
    </>
  )
}
