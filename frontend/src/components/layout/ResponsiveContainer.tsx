import { forwardRef } from 'react'
import clsx from 'clsx'

interface ResponsiveContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  center?: boolean
  fluid?: boolean
}

/**
 * 增强的响应式容器组件
 * 支持多种尺寸、内边距和布局选项
 */
export const ResponsiveContainer = forwardRef<HTMLDivElement, ResponsiveContainerProps>(
  ({ 
    size = 'xl', 
    padding = 'md', 
    center = true, 
    fluid = false, 
    className, 
    children, 
    ...props 
  }, ref) => {
    const sizeClasses = {
      sm: 'max-w-2xl',
      md: 'max-w-4xl', 
      lg: 'max-w-6xl',
      xl: 'max-w-7xl',
      full: 'max-w-none'
    }

    const paddingClasses = {
      none: '',
      sm: 'px-4 sm:px-6',
      md: 'px-4 sm:px-6 lg:px-8',
      lg: 'px-6 sm:px-8 lg:px-12',
      xl: 'px-8 sm:px-12 lg:px-16'
    }

    return (
      <div
        ref={ref}
        className={clsx(
          // 基础样式
          'w-full',
          // 尺寸控制
          !fluid && sizeClasses[size],
          // 居中
          center && 'mx-auto',
          // 内边距
          paddingClasses[padding],
          // 自定义类名
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)

ResponsiveContainer.displayName = 'ResponsiveContainer'
