'use client'

import { Fragment, useEffect, useRef, useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Popover, Transition } from '@headlessui/react'
import clsx from 'clsx'

import { Container } from '@/components/layout/Container'
import avatarImage from '@/images/avatar.jpg'
import { getHeaderNavigation, getPersonalInfo } from '@/lib/api'
import { ThemeToggle } from '@/components/shared/ThemeToggle'
import { ChevronDownIcon, XIcon } from 'lucide-react'

import TypingAnimation from "@/components/ui/typing-animation";

function MobileNavItem({
  href,
  children,
}: {
  href: string
  children: React.ReactNode
}) {
  return (
    <li>
      <Popover.Button as={Link} href={href} prefetch={true} className="block py-2">
        {children}
      </Popover.Button>
    </li>
  )
}

function MobileNavigation({
  navItems,
  ...props
}: React.ComponentPropsWithoutRef<typeof Popover> & {
  navItems: Array<{name: string, href: string, icon?: string}>
}) {
  return (
    <Popover {...props}>
      <Popover.Button className="group flex items-center rounded-full px-4 py-2 text-sm font-medium shadow-lg ring-1 ring-border backdrop-blur-md bg-card/95 transition-all duration-300 hover:shadow-xl hover:ring-primary/20 hover:bg-primary/5">
        Menu
        <ChevronDownIcon className="ml-3 h-auto w-2 transition-transform duration-200 group-hover:rotate-180" />
      </Popover.Button>
      <Transition.Root>
        <Transition.Child
          as={Fragment}
          enter="duration-150 ease-out"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="duration-150 ease-in"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <Popover.Overlay className="fixed inset-0 z-50 backdrop-blur-sm dark:bg-background/80" />
        </Transition.Child>
        <Transition.Child
          as={Fragment}
          enter="duration-150 ease-out"
          enterFrom="opacity-0 scale-95"
          enterTo="opacity-100 scale-100"
          leave="duration-150 ease-in"
          leaveFrom="opacity-100 scale-100"
          leaveTo="opacity-0 scale-95"
        >
          <Popover.Panel
            focus
            className="fixed inset-x-4 top-8 z-50 origin-top rounded-3xl p-8 ring-1 ring-muted bg-card"
          >
            <div className="flex flex-row-reverse items-center justify-between">
              <Popover.Button aria-label="Close menu" className="-m-1 p-1">
                <XIcon className="h-6 w-6 text-muted-foreground" />
              </Popover.Button>
              <h2 className="text-sm font-medium text-muted-foreground">
                Navigation
              </h2>
            </div>
            <nav className="mt-6">
              <ul className="-my-2 divide-y divide-zinc-100 text-base dark:divide-zinc-100/5">
                {navItems.map((item) => (
                  <MobileNavItem key={item.name} href={item.href}>{item.name}</MobileNavItem>
                ))}
              </ul>
            </nav>
          </Popover.Panel>
        </Transition.Child>
      </Transition.Root>
    </Popover>
  )
}

function NavItem({
  href,
  children,
}: {
  href: string
  children: React.ReactNode
}) {
  let isActive = usePathname() === href

  return (
    <li>
      <Link
        href={href}
        prefetch={true}
        className={clsx(
          'relative block px-3 py-2 transition-all duration-300 rounded-md',
          isActive
            ? 'text-primary bg-primary/10'
            : 'opacity-80 hover:opacity-100 hover:text-primary hover:bg-primary/5',
        )}
      >
        {children}
        {isActive && (
          <span className="absolute inset-x-1 -bottom-px h-[2px] bg-gradient-to-r from-primary/0 via-primary/60 to-primary/0 rounded-full animate-pulse-soft" />
        )}
      </Link>
    </li>
  )
}

function DesktopNavigation({
  navItems,
  ...props
}: React.ComponentPropsWithoutRef<'nav'> & {
  navItems: Array<{name: string, href: string, icon?: string}>
}) {
  return (
    <nav {...props}>
      <ul className="flex rounded-full px-3 text-sm font-medium bg-card/95 ring-1 ring-border shadow-lg backdrop-blur-md transition-all duration-300 hover:shadow-xl hover:ring-primary/20">
        {navItems.map((item, index) => (
          <Fragment key={item.name}>
            {index > 0 && (
              <li className="flex items-center">
                <div className="h-4 w-px bg-muted-foreground/30" />
              </li>
            )}
            <NavItem href={item.href}>{item.name}</NavItem>
          </Fragment>
        ))}
      </ul>
    </nav>
  )
}



function clamp(number: number, a: number, b: number) {
  let min = Math.min(a, b)
  let max = Math.max(a, b)
  return Math.min(Math.max(number, min), max)
}

function AvatarContainer({
  showName = false,
  personalName = 'Jay-Yao',
  className,
  ...props
}: React.ComponentPropsWithoutRef<'div'> & {
  showName?: boolean
  personalName?: string
}) {
  return (
    <div className='flex flex-row items-center gap-2'>
      <div
        className={clsx(
          className,
          'h-10 w-10 rounded-full bg-white/90 p-0.5 shadow-lg shadow-zinc-800/5 ring-1 ring-zinc-900/5 backdrop-blur dark:bg-zinc-800/90 dark:ring-white/10',
        )}
        {...props}
      />
      {showName && (
        <Link
          href="/"
          aria-label="Home"
          className='pointer-events-auto'
        >
          <div className="text-md font-semibold capitalize">{personalName}</div>
        </Link>
      )}
    </div>
  )
}

function Avatar({
  large = false,
  className,
  ...props
}: Omit<React.ComponentPropsWithoutRef<typeof Link>, 'href'> & {
  large?: boolean
}) {
  return (
    <Link
      href="/"
      aria-label="Home"
      className={clsx(className, 'pointer-events-auto')}
      {...props}
    >
      <Image
        src={avatarImage}
        alt=""
        sizes={large ? '4rem' : '2.25rem'}
        className={clsx(
          'rounded-full bg-zinc-100 object-cover dark:bg-zinc-800',
          large ? 'h-16 w-16' : 'h-9 w-9',
        )}
        priority
      />
    </Link>
  )
}

export function Header() {
  let isHomePage = usePathname() === '/'
  const [navItems, setNavItems] = useState<Array<{name: string, href: string, icon?: string}>>([
    { name: 'Home', href: '/' },
    { name: 'About', href: '/about' },
    { name: 'Projects', href: '/projects' },
    { name: 'Blogs', href: '/blogs' },
    { name: 'Gallery', href: '/gallery' }
  ]);

  // 添加个人信息状态
  const [personalName, setPersonalName] = useState('Jay-Yao'); // 默认值

  let headerRef = useRef<React.ElementRef<'div'>>(null)
  let avatarRef = useRef<React.ElementRef<'div'>>(null)
  let isInitial = useRef(true)

  useEffect(() => {
    const loadNavigation = () => {
      getHeaderNavigation()
        .then((navigationData) => {
          const items = navigationData.map(item => ({
            name: item.name,
            href: item.href,
            icon: item.icon
          }));
          setNavItems(items);
        })
        .catch((error) => {
          // Failed to load navigation (silently handled in production)
          // 保持默认值，不需要额外处理
        });
    };

    const loadPersonalInfo = () => {
      getPersonalInfo()
        .then((personalInfo) => {
          setPersonalName(personalInfo.name);
        })
        .catch((error) => {
          // Failed to load personal info, keep default value
          
        });
    };

    loadNavigation();
    loadPersonalInfo();
  }, []);

  useEffect(() => {
    let downDelay = avatarRef.current?.offsetTop ?? 0
    let upDelay = 64

    function setProperty(property: string, value: string) {
      document.documentElement.style.setProperty(property, value)
    }

    function removeProperty(property: string) {
      document.documentElement.style.removeProperty(property)
    }

    function updateHeaderStyles() {
      if (!headerRef.current) {
        return
      }

      let { top, height } = headerRef.current.getBoundingClientRect()
      let scrollY = clamp(
        window.scrollY,
        0,
        document.body.scrollHeight - window.innerHeight,
      )

      if (isInitial.current) {
        setProperty('--header-position', 'sticky')
      }

      setProperty('--content-offset', `${downDelay}px`)

      if (isInitial.current || scrollY < downDelay) {
        setProperty('--header-height', `${downDelay + height}px`)
        setProperty('--header-mb', `${-downDelay}px`)
      } else if (top + height < -upDelay) {
        let offset = Math.max(height, scrollY - upDelay)
        setProperty('--header-height', `${offset}px`)
        setProperty('--header-mb', `${height - offset}px`)
      } else if (top === 0) {
        setProperty('--header-height', `${scrollY + height}px`)
        setProperty('--header-mb', `${-scrollY}px`)
      }

      // if (top === 0 && scrollY > 0 && scrollY >= downDelay) {
      //   setProperty('--header-inner-position', 'fixed')
      //   removeProperty('--header-top')
      //   removeProperty('--avatar-top')
      // } else {
      //   removeProperty('--header-inner-position')
      //   setProperty('--header-top', '0px')
      //   setProperty('--avatar-top', '0px')
      // }
    }

    function updateAvatarStyles() {
      if (!isHomePage) {
        return
      }

      let fromScale = 1
      let toScale = 36 / 64
      let fromX = 0
      let toX = 2 / 16

      let scrollY = downDelay - window.scrollY

      let scale = (scrollY * (fromScale - toScale)) / downDelay + toScale
      scale = clamp(scale, fromScale, toScale)

      let x = (scrollY * (fromX - toX)) / downDelay + toX
      x = clamp(x, fromX, toX)

      setProperty(
        '--avatar-image-transform',
        `translate3d(${x}rem, 0, 0) scale(${scale})`,
      )
      setProperty('--avatar-hi-opacity', scale === toScale ? '0' : '1')
    }

    function updateHiStyles() {
      if (!isHomePage) {
        return
      }

      // 当滚动超过一定距离时，完全隐藏 Hi
      let opacity = window.scrollY < 50 ? 1 : 0

      setProperty('--avatar-hi-opacity', opacity.toString())
    }



    function updateStyles() {
      updateHeaderStyles()
      updateAvatarStyles()
      updateHiStyles()
      isInitial.current = false
    }

    updateStyles()
    window.addEventListener('scroll', updateStyles, { passive: true })
    window.addEventListener('resize', updateStyles)

    return () => {
      window.removeEventListener('scroll', updateStyles)
      window.removeEventListener('resize', updateStyles)
    }
  }, [isHomePage])

  return (
    <>
      <header
        className="pointer-events-none relative z-50 flex flex-none flex-col"
        style={{
          height: 'var(--header-height)',
          marginBottom: 'var(--header-mb)',
        }}
      >
        {isHomePage && (
          <>
            <div
              ref={avatarRef}
              className="order-last mt-[calc(theme(spacing.16)-theme(spacing.3))]"
            />
            <Container
              className="top-0 order-last -mb-3 pt-3"
              style={{
                position:
                  'var(--header-position)' as React.CSSProperties['position'],
              }}
            >
              <div
                className="top-[var(--avatar-top,theme(spacing.3))] w-full"
                style={{
                  position:
                    'var(--header-inner-position)' as React.CSSProperties['position'],
                }}
              >
                <div className="relative">
                  <AvatarContainer
                    className="absolute left-0 top-3 origin-left transition-opacity"
                    style={{
                      opacity: 'var(--avatar-border-opacity, 0)',
                      transform: 'var(--avatar-border-transform)',
                    }}
                  />
                  <div className="flex flex-row items-center gap-4">
                    <Avatar
                      large
                      className="block h-16 w-16 origin-left"
                      style={{ transform: 'var(--avatar-image-transform)' }}
                    />
                    <div
                      className="text-3xl md:text-6xl font-bold tracking-tight flex flex-row"
                      style={{
                        opacity: 'var(--avatar-hi-opacity, 0)',
                        transform: 'var(--avatar-hi-transform)'
                      }}
                    >
                      Hi,{' '}
                      <TypingAnimation
                        className="text-3xl md:text-6xl font-bold tracking-tight"
                        text={`I'm ${personalName} `}
                        duration={150}
                      />
                      👋
                    </div>
                  </div>
                </div>
              </div>
            </Container>
          </>
        )}
        <div
          ref={headerRef}
          className="top-0 z-10 h-16 pt-6"
          style={{
            position:
              'var(--header-position)' as React.CSSProperties['position'],
          }}
        >
          <Container
            className="top-[var(--header-top,theme(spacing.6))] w-full"
            style={{
              position:
                'var(--header-inner-position)' as React.CSSProperties['position'],
            }}
          >
            <div className="relative flex gap-4">
              <div className="flex flex-1">
                {!isHomePage && (
                  <AvatarContainer showName={true} personalName={personalName}>
                    <Avatar />
                  </AvatarContainer>
                )}
              </div>
              <div className="flex flex-1 justify-end md:justify-center">
                <MobileNavigation navItems={navItems} className="pointer-events-auto md:hidden" />
                <DesktopNavigation navItems={navItems} className="pointer-events-auto hidden md:block" />
              </div>
              <div className="flex justify-end md:flex-1">
                <div className="pointer-events-auto flex flex-row items-center gap-2 md:mr-2">
                  <ThemeToggle />
                </div>
              </div>
            </div>
          </Container>
        </div>
      </header>
      {isHomePage && (
        <div
          className="flex-none"
          style={{ height: 'var(--content-offset)' }}
        />
      )}
    </>
  )
}
