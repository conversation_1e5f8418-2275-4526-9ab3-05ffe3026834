import { forwardRef } from 'react'
import clsx from 'clsx'

interface EnhancedReadingLayoutProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * 内容类型，影响最佳阅读宽度
   */
  contentType?: 'article' | 'documentation' | 'showcase'
  
  /**
   * 是否启用动态字体缩放
   */
  dynamicFontScaling?: boolean
  
  /**
   * 是否启用视觉呼吸空间
   */
  breathingSpace?: boolean
  
  /**
   * 侧边栏内容（如目录）
   */
  sidebar?: React.ReactNode
  
  /**
   * 是否启用沉浸式阅读模式
   */
  immersiveMode?: boolean
}

/**
 * 红点奖级增强阅读布局
 * 基于人体工程学和视觉心理学优化的阅读体验
 */
export const EnhancedReadingLayout = forwardRef<HTMLDivElement, EnhancedReadingLayoutProps>(
  ({ 
    contentType = 'article',
    dynamicFontScaling = true,
    breathingSpace = true,
    sidebar,
    immersiveMode = false,
    className, 
    children, 
    ...props 
  }, ref) => {
    
    // 根据内容类型优化布局
    const contentTypeStyles = {
      article: {
        // 文章：优化行长度，65-75字符最佳
        maxWidth: 'max-w-4xl lg:max-w-5xl xl:max-w-6xl',
        fontSize: dynamicFontScaling ? 'text-base lg:text-lg xl:text-xl' : 'text-base',
        lineHeight: 'leading-relaxed lg:leading-loose'
      },
      documentation: {
        // 文档：稍宽布局，便于代码和图表展示
        maxWidth: 'max-w-5xl lg:max-w-6xl xl:max-w-7xl',
        fontSize: dynamicFontScaling ? 'text-sm lg:text-base xl:text-lg' : 'text-base',
        lineHeight: 'leading-normal lg:leading-relaxed'
      },
      showcase: {
        // 展示：最宽布局，突出视觉内容
        maxWidth: 'max-w-6xl lg:max-w-7xl xl:max-w-none',
        fontSize: dynamicFontScaling ? 'text-base lg:text-lg xl:text-xl' : 'text-base',
        lineHeight: 'leading-relaxed'
      }
    }

    const currentStyle = contentTypeStyles[contentType]

    // 沉浸式模式样式
    const immersiveStyles = immersiveMode ? {
      background: 'linear-gradient(135deg, rgba(var(--background-rgb), 0.95) 0%, rgba(var(--background-rgb), 0.98) 100%)',
      backdropFilter: 'blur(20px)',
      border: '1px solid rgba(var(--border-rgb), 0.1)',
      borderRadius: '24px',
      padding: '2rem lg:3rem xl:4rem',
      boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05)'
    } : {}

    return (
      <div
        ref={ref}
        className={clsx(
          // 基础布局
          'mx-auto w-full',
          currentStyle.maxWidth,
          // 渐进式边距
          'px-4 sm:px-6 lg:px-8 xl:px-12 2xl:px-16',
          // 呼吸空间
          breathingSpace && 'py-8 lg:py-12 xl:py-16',
          // 字体和行高
          currentStyle.fontSize,
          currentStyle.lineHeight,
          // 自定义类名
          className
        )}
        style={immersiveStyles}
        {...props}
      >
        {sidebar ? (
          // 带侧边栏的布局
          <div className="flex flex-col lg:flex-row gap-8 lg:gap-12 xl:gap-16">
            {/* 主内容区域 */}
            <main className="flex-1 min-w-0">
              {children}
            </main>
            
            {/* 侧边栏 */}
            <aside className="lg:w-80 xl:w-96 flex-shrink-0">
              <div className="sticky top-24">
                {sidebar}
              </div>
            </aside>
          </div>
        ) : (
          // 单栏布局
          <main className="w-full">
            {children}
          </main>
        )}
      </div>
    )
  }
)

EnhancedReadingLayout.displayName = 'EnhancedReadingLayout'

/**
 * 预设的阅读布局组件
 */
export const ArticleLayout = forwardRef<HTMLDivElement, Omit<EnhancedReadingLayoutProps, 'contentType'>>(
  (props, ref) => <EnhancedReadingLayout ref={ref} contentType="article" {...props} />
)

export const DocumentationLayout = forwardRef<HTMLDivElement, Omit<EnhancedReadingLayoutProps, 'contentType'>>(
  (props, ref) => <EnhancedReadingLayout ref={ref} contentType="documentation" {...props} />
)

export const ShowcaseLayout = forwardRef<HTMLDivElement, Omit<EnhancedReadingLayoutProps, 'contentType'>>(
  (props, ref) => <EnhancedReadingLayout ref={ref} contentType="showcase" {...props} />
)

ArticleLayout.displayName = 'ArticleLayout'
DocumentationLayout.displayName = 'DocumentationLayout'
ShowcaseLayout.displayName = 'ShowcaseLayout'
