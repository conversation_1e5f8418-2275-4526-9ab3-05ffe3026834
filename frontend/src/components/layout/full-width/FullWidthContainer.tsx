import { forwardRef } from 'react'
import clsx from 'clsx'

interface FullWidthContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * 布局模式
   * - 'reading': 优化阅读体验的宽度（推荐用于博客文章）
   * - 'showcase': 展示型宽度（推荐用于项目展示）
   * - 'full': 完全全宽（推荐用于图片画廊）
   */
  mode?: 'reading' | 'showcase' | 'full'
  
  /**
   * 是否启用渐进式边距
   * 在大屏幕上提供更好的视觉平衡
   */
  progressiveMargins?: boolean
  
  /**
   * 是否启用视觉引导线
   * 在超宽屏幕上提供视觉边界
   */
  visualGuides?: boolean
}

/**
 * 红点奖级全宽度容器组件
 * 专为现代大屏幕设计，提供卓越的阅读和浏览体验
 */
export const FullWidthContainer = forwardRef<HTMLDivElement, FullWidthContainerProps>(
  ({ 
    mode = 'reading',
    progressiveMargins = true,
    visualGuides = false,
    className, 
    children, 
    ...props 
  }, ref) => {
    
    // 根据模式定义不同的宽度策略
    const modeClasses = {
      // 阅读模式：保持良好的行长度，但在大屏幕上扩展
      reading: clsx(
        'max-w-3xl md:max-w-4xl lg:max-w-6xl',
        'xl:max-w-none 2xl:max-w-none',
        progressiveMargins && 'xl:mx-[8%] 2xl:mx-[12%] 3xl:mx-[16%]'
      ),
      
      // 展示模式：更宽的布局，适合项目展示
      showcase: clsx(
        'max-w-4xl lg:max-w-6xl xl:max-w-7xl',
        '2xl:max-w-none',
        progressiveMargins && '2xl:mx-[6%] 3xl:mx-[10%]'
      ),
      
      // 全宽模式：充分利用屏幕空间
      full: clsx(
        'max-w-none',
        progressiveMargins && 'px-4 sm:px-6 lg:px-8 xl:px-12 2xl:px-16'
      )
    }

    // 视觉引导线样式
    const visualGuideStyles = visualGuides ? {
      position: 'relative' as const,
      '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: '8%',
        right: '8%',
        height: '100%',
        border: '1px solid rgba(var(--primary-rgb), 0.1)',
        borderTop: 'none',
        borderBottom: 'none',
        pointerEvents: 'none',
        zIndex: -1
      }
    } : {}

    return (
      <div
        ref={ref}
        className={clsx(
          // 基础样式
          'mx-auto w-full',
          // 模式特定样式
          modeClasses[mode],
          // 渐进式内边距
          !progressiveMargins && mode !== 'full' && 'px-4 lg:px-8 xl:px-16 2xl:px-24',
          // 自定义类名
          className
        )}
        style={visualGuideStyles}
        {...props}
      >
        {children}
      </div>
    )
  }
)

FullWidthContainer.displayName = 'FullWidthContainer'

/**
 * 预设的布局组件
 */
export const ReadingContainer = forwardRef<HTMLDivElement, Omit<FullWidthContainerProps, 'mode'>>(
  (props, ref) => <FullWidthContainer ref={ref} mode="reading" {...props} />
)

export const ShowcaseContainer = forwardRef<HTMLDivElement, Omit<FullWidthContainerProps, 'mode'>>(
  (props, ref) => <FullWidthContainer ref={ref} mode="showcase" {...props} />
)

export const FullContainer = forwardRef<HTMLDivElement, Omit<FullWidthContainerProps, 'mode'>>(
  (props, ref) => <FullWidthContainer ref={ref} mode="full" {...props} />
)

ReadingContainer.displayName = 'ReadingContainer'
ShowcaseContainer.displayName = 'ShowcaseContainer'
FullContainer.displayName = 'FullContainer'
