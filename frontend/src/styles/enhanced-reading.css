/* 红点奖级增强阅读体验样式 */

/* 渐进式字体缩放 */
@media (min-width: 1024px) {
  .enhanced-reading-text {
    font-size: 1.125rem; /* 18px */
    line-height: 1.75; /* 28px */
  }
}

@media (min-width: 1280px) {
  .enhanced-reading-text {
    font-size: 1.25rem; /* 20px */
    line-height: 1.8; /* 36px */
  }
}

@media (min-width: 1536px) {
  .enhanced-reading-text {
    font-size: 1.375rem; /* 22px */
    line-height: 1.8; /* 39.6px */
  }
}

/* 视觉引导线 */
.visual-guide-container {
  position: relative;
}

.visual-guide-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 8%;
  right: 8%;
  height: 100%;
  border-left: 1px solid rgba(var(--primary-rgb, 16, 185, 129), 0.1);
  border-right: 1px solid rgba(var(--primary-rgb, 16, 185, 129), 0.1);
  pointer-events: none;
  z-index: -1;
}

/* 沉浸式阅读模式 */
.immersive-reading {
  background: linear-gradient(135deg, 
    rgba(var(--background-rgb, 255, 255, 255), 0.95) 0%, 
    rgba(var(--background-rgb, 255, 255, 255), 0.98) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(var(--border-rgb, 229, 231, 235), 0.1);
  border-radius: 24px;
  padding: 2rem;
  box-shadow: 
    0 25px 50px -12px rgba(0, 0, 0, 0.25), 
    0 0 0 1px rgba(255, 255, 255, 0.05);
}

@media (min-width: 1024px) {
  .immersive-reading {
    padding: 3rem;
  }
}

@media (min-width: 1280px) {
  .immersive-reading {
    padding: 4rem;
  }
}

/* 呼吸空间优化 */
.breathing-space {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

@media (min-width: 1024px) {
  .breathing-space {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }
}

@media (min-width: 1280px) {
  .breathing-space {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }
}

/* 渐进式边距 */
.progressive-margins {
  margin-left: auto;
  margin-right: auto;
}

@media (min-width: 1280px) {
  .progressive-margins {
    margin-left: 8%;
    margin-right: 8%;
  }
}

@media (min-width: 1536px) {
  .progressive-margins {
    margin-left: 12%;
    margin-right: 12%;
  }
}

@media (min-width: 1920px) {
  .progressive-margins {
    margin-left: 16%;
    margin-right: 16%;
  }
}

/* 优化的段落间距 */
.enhanced-prose p {
  margin-bottom: 1.5rem;
}

.enhanced-prose p:last-child {
  margin-bottom: 0;
}

/* 标题层次优化 */
.enhanced-prose h1 {
  font-size: 2.25rem;
  line-height: 1.2;
  margin-bottom: 2rem;
  margin-top: 3rem;
}

.enhanced-prose h2 {
  font-size: 1.875rem;
  line-height: 1.3;
  margin-bottom: 1.5rem;
  margin-top: 2.5rem;
}

.enhanced-prose h3 {
  font-size: 1.5rem;
  line-height: 1.4;
  margin-bottom: 1rem;
  margin-top: 2rem;
}

@media (min-width: 1024px) {
  .enhanced-prose h1 {
    font-size: 2.5rem;
  }
  
  .enhanced-prose h2 {
    font-size: 2rem;
  }
  
  .enhanced-prose h3 {
    font-size: 1.75rem;
  }
}

/* 侧边栏优化 */
.enhanced-sidebar {
  position: sticky;
  top: 6rem;
  max-height: calc(100vh - 8rem);
  overflow-y: auto;
}

/* 滚动条美化 */
.enhanced-sidebar::-webkit-scrollbar {
  width: 4px;
}

.enhanced-sidebar::-webkit-scrollbar-track {
  background: rgba(var(--muted-rgb, 156, 163, 175), 0.1);
  border-radius: 2px;
}

.enhanced-sidebar::-webkit-scrollbar-thumb {
  background: rgba(var(--primary-rgb, 16, 185, 129), 0.3);
  border-radius: 2px;
}

.enhanced-sidebar::-webkit-scrollbar-thumb:hover {
  background: rgba(var(--primary-rgb, 16, 185, 129), 0.5);
}

/* 响应式优化 */
@media (max-width: 1023px) {
  .enhanced-reading-text {
    font-size: 1rem;
    line-height: 1.6;
  }
  
  .immersive-reading {
    border-radius: 16px;
    padding: 1.5rem;
  }
  
  .breathing-space {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }
}

/* 动画和过渡效果 */
.enhanced-container {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.enhanced-container:hover {
  transform: translateY(-2px);
}

/* 焦点状态优化 */
.enhanced-container:focus-within {
  outline: 2px solid rgba(var(--primary-rgb, 16, 185, 129), 0.5);
  outline-offset: 4px;
}

/* 打印样式优化 */
@media print {
  .enhanced-reading-text {
    font-size: 12pt;
    line-height: 1.5;
  }
  
  .immersive-reading {
    background: white;
    border: none;
    box-shadow: none;
    padding: 0;
  }
  
  .enhanced-sidebar {
    display: none;
  }
}
