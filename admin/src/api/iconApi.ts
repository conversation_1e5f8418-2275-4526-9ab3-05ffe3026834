/**
 * 图标系统API服务
 */

import axios from 'axios';
import {
  IconLibrary,
  IconCategory,
  IconMetadata,
  IconSearchRequest,
  IconSearchResponse,
  IconStats,
  IconInsertData,
  IconRenderRequest
} from '../types/icon';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api';

// 创建axios实例
const iconApi = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
});

// 请求拦截器
iconApi.interceptors.request.use(
  (config) => {
    // 添加认证token等
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
iconApi.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    console.error('Icon API Error:', error);
    return Promise.reject(error);
  }
);

export class IconApiService {
  // 图标库管理
  static async getLibraries(): Promise<IconLibrary[]> {
    return iconApi.get('/icons/libraries');
  }

  static async createLibrary(data: Partial<IconLibrary>): Promise<IconLibrary> {
    return iconApi.post('/icons/libraries', data);
  }

  static async updateLibrary(id: number, data: Partial<IconLibrary>): Promise<IconLibrary> {
    return iconApi.put(`/icons/libraries/${id}`, data);
  }

  static async deleteLibrary(id: number): Promise<void> {
    return iconApi.delete(`/icons/libraries/${id}`);
  }

  // 图标分类管理
  static async getCategories(libraryId?: number): Promise<IconCategory[]> {
    return iconApi.get('/icons/categories', {
      params: {
        library_id: libraryId
      }
    });
  }

  static async createCategory(data: Partial<IconCategory>): Promise<IconCategory> {
    return iconApi.post('/icons/categories', data);
  }

  static async updateCategory(id: number, data: Partial<IconCategory>): Promise<IconCategory> {
    return iconApi.put(`/icons/categories/${id}`, data);
  }

  static async deleteCategory(id: number): Promise<void> {
    return iconApi.delete(`/icons/categories/${id}`);
  }

  // 图标搜索
  static async searchIcons(searchRequest: IconSearchRequest): Promise<IconSearchResponse> {
    return iconApi.post('/icons/search', searchRequest);
  }

  static async searchIconsGet(params: Partial<IconSearchRequest>): Promise<IconSearchResponse> {
    return iconApi.get('/icons/search', { params });
  }

  // 图标渲染
  static async renderIcon(library: string, iconKey: string, theme: string = 'light', size: number = 24): Promise<string> {
    const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api';
    const response = await axios.get(`${baseUrl}/icons/render/${library}/${iconKey}`, {
      params: { theme, size },
      responseType: 'text',
      timeout: 10000
    });
    return response.data;
  }

  static async testIconRender(library: string, iconKey: string, theme: string = 'light'): Promise<{
    success: boolean;
    renderable: boolean;
    error?: string;
  }> {
    return iconApi.get(`/icons/test-render/${library}/${iconKey}`, {
      params: { theme }
    });
  }



  // 最近使用的图标
  static async getRecentIcons(limit: number = 20, userId?: number): Promise<IconMetadata[]> {
    return iconApi.get('/icons/recent', {
      params: { limit, user_id: userId }
    });
  }

  // 图标统计
  static async getIconStats(): Promise<IconStats> {
    return iconApi.get('/icons/stats');
  }

  // 批量操作
  static async batchToggleFavorites(iconIds: number[], userId?: number, sessionId?: string): Promise<{
    success: number;
    failed: number;
    results: Array<{ iconId: number; success: boolean; error?: string }>;
  }> {
    return iconApi.post('/icons/favorites/batch', {
      icon_ids: iconIds,
      user_id: userId,
      session_id: sessionId
    });
  }

  // 图标元数据管理
  static async getIconMetadata(id: number): Promise<IconMetadata> {
    return iconApi.get(`/icons/metadata/${id}`);
  }

  static async updateIconMetadata(id: number, data: Partial<IconMetadata>): Promise<IconMetadata> {
    return iconApi.put(`/icons/metadata/${id}`, data);
  }

  static async deleteIconMetadata(id: number): Promise<void> {
    return iconApi.delete(`/icons/metadata/${id}`);
  }
}
