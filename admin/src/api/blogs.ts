import axiosInstance from './axiosInstance';
import {
  BlogResponse,
  BlogCreate,
  BlogUpdate,
  BlogVersionSummary,
  BlogVersionResponse,
  BlogVersionCreate,
  VersionComparison
} from '../types/blog'; // Import BlogCreate and BlogUpdate
import { log } from '../utils/logger';

interface PaginatedBlogResponse {
  items: BlogResponse[];
  // Backend currently doesn't return total, so we omit it
  // total: number;
}

export const getBlogs = async (
  page: number = 1,
  limit: number = 10,
  search?: string, // Add optional search parameter
  tag?: string // Add tag parameter
): Promise<BlogResponse[]> => {
  const params = new URLSearchParams();
  params.append('skip', String((page - 1) * limit));
  params.append('limit', String(limit));
  params.append('published_only', 'false'); // Fetch both published and unpublished for admin
  if (search) {
    params.append('search', search);
  }
  if (tag) { // Add tag to params if provided
    params.append('tag', tag);
  }
  
  log.api('GET', '/blogs', { params: params.toString() });
  const response = await axiosInstance.get<BlogResponse[]>('/blogs', { params });
  return response.data;
};

// 添加创建博客的 API 函数
export const createBlog = async (blogData: BlogCreate): Promise<BlogResponse> => {
  const response = await axiosInstance.post<BlogResponse>('/blogs', blogData);
  return response.data;
};

// 添加根据 slug 获取博客的 API 函数
export const getBlogBySlug = async (slug: string): Promise<BlogResponse> => {
  const response = await axiosInstance.get<BlogResponse>(`/blogs/${slug}`);
  return response.data;
};

// 添加更新博客的 API 函数
export const updateBlog = async (slug: string, blogData: BlogUpdate): Promise<BlogResponse> => {
  const response = await axiosInstance.put<BlogResponse>(`/blogs/${slug}`, blogData);
  return response.data;
};

// 添加删除博客的 API 函数
export const deleteBlog = async (slug: string): Promise<void> => {
  await axiosInstance.delete(`/blogs/${slug}`);
};

// 添加部分更新博客的 API 函数（用于更新主页显示等状态）
export const patchBlog = async (slug: string, updateData: Partial<BlogUpdate>): Promise<BlogResponse> => {
  const response = await axiosInstance.patch<BlogResponse>(`/blogs/${slug}`, updateData);
  return response.data;
};

// ==================== 版本历史相关API ====================

// 获取博客版本历史列表
export const getBlogVersions = async (
  slug: string,
  limit: number = 20,
  offset: number = 0
): Promise<BlogVersionSummary[]> => {
  const response = await axiosInstance.get(`/blogs/${slug}/versions`, {
    params: { limit, offset }
  });
  return response.data;
};

// 获取特定版本详情
export const getBlogVersion = async (
  slug: string,
  versionNumber: number
): Promise<BlogVersionResponse> => {
  const response = await axiosInstance.get(`/blogs/${slug}/versions/${versionNumber}`);
  return response.data;
};

// 恢复到指定版本
export const restoreBlogVersion = async (
  slug: string,
  versionNumber: number
): Promise<BlogResponse> => {
  const response = await axiosInstance.post(`/blogs/${slug}/versions/${versionNumber}/restore`);
  return response.data;
};

// 比较两个版本
export const compareBlogVersions = async (
  slug: string,
  version1: number,
  version2: number
): Promise<VersionComparison> => {
  const response = await axiosInstance.get(`/blogs/${slug}/versions/compare/${version1}/${version2}`);
  return response.data;
};

// 手动创建版本快照
export const createBlogVersion = async (
  slug: string,
  versionData: BlogVersionCreate
): Promise<BlogVersionResponse> => {
  const response = await axiosInstance.post(`/blogs/${slug}/versions`, versionData);
  return response.data;
};

// 基于当前内容创建版本快照
export const createBlogVersionWithContent = async (
  slug: string,
  contentData: any
): Promise<BlogVersionResponse> => {
  const response = await axiosInstance.post(`/blogs/${slug}/versions/snapshot`, contentData);
  return response.data;
};

// 更新版本信息
export const updateBlogVersion = async (
  slug: string,
  versionNumber: number,
  data: { change_summary?: string; description?: string; is_major_change?: boolean }
): Promise<BlogVersionResponse> => {
  const response = await axiosInstance.put(`/blogs/${slug}/versions/${versionNumber}`, data);
  return response.data;
};

// 删除单个版本
export const deleteBlogVersion = async (
  slug: string,
  versionNumber: number
): Promise<void> => {
  await axiosInstance.delete(`/blogs/${slug}/versions/${versionNumber}`);
};

// 批量删除版本
export const deleteBlogVersions = async (
  slug: string,
  versionNumbers: number[]
): Promise<void> => {
  await axiosInstance.delete(`/blogs/${slug}/versions/batch`, {
    data: { version_numbers: versionNumbers }
  });
};

// Add delete blog API function later