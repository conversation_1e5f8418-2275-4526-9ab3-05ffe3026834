// Slug API服务

import axiosInstance from './axiosInstance';

export interface SlugGenerateRequest {
  text: string;
  model_type?: string;
  existing_slugs?: string[];
}

export interface SlugGenerateResponse {
  slug: string;
  is_valid: boolean;
  is_unique: boolean;
  suggestions?: string[];
}

export interface SlugValidateRequest {
  slug: string;
  model_type?: string;
  exclude_id?: number;
}

export interface SlugValidateResponse {
  is_valid: boolean;
  is_unique: boolean;
  errors: string[];
  suggestions?: string[];
}

export interface BatchSlugRequest {
  texts: string[];
  model_type?: string;
  ensure_unique?: boolean;
}

export interface BatchSlugResponse {
  slugs: string[];
  total_count: number;
  unique_count: number;
}

// 生成slug
export const generateSlug = async (request: SlugGenerateRequest): Promise<SlugGenerateResponse> => {
  const response = await axiosInstance.post('/api/slug/generate', request);
  return response.data;
};

// 验证slug
export const validateSlug = async (request: SlugValidateRequest): Promise<SlugValidateResponse> => {
  const response = await axiosInstance.post('/api/slug/validate', request);
  return response.data;
};

// 批量生成slug
export const batchGenerateSlug = async (request: BatchSlugRequest): Promise<BatchSlugResponse> => {
  const response = await axiosInstance.post('/api/slug/batch-generate', request);
  return response.data;
};

// 清理文件名
export const cleanFilename = async (filename: string): Promise<{ original: string; cleaned: string }> => {
  const response = await axiosInstance.post('/api/slug/clean-filename', null, {
    params: { filename }
  });
  return response.data;
};

// 便捷函数：生成特定类型的slug
export const generateCategorySlug = async (name: string, existingSlugs: string[] = []) => {
  return generateSlug({
    text: name,
    model_type: 'image_category',
    existing_slugs: existingSlugs
  });
};

export const generateArticleSlug = async (title: string, existingSlugs: string[] = []) => {
  return generateSlug({
    text: title,
    model_type: 'article',
    existing_slugs: existingSlugs
  });
};

export const generateBlogSlug = async (title: string, existingSlugs: string[] = []) => {
  return generateSlug({
    text: title,
    model_type: 'blog',
    existing_slugs: existingSlugs
  });
};

export const generateProjectSlug = async (title: string, existingSlugs: string[] = []) => {
  return generateSlug({
    text: title,
    model_type: 'project',
    existing_slugs: existingSlugs
  });
};

export const generateTagSlug = async (name: string, existingSlugs: string[] = []) => {
  return generateSlug({
    text: name,
    model_type: 'tag',
    existing_slugs: existingSlugs
  });
};

export default {
  generateSlug,
  validateSlug,
  batchGenerateSlug,
  cleanFilename,
  generateCategorySlug,
  generateArticleSlug,
  generateBlogSlug,
  generateProjectSlug,
  generateTagSlug
};
