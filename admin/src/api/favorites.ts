/**
 * 图标收藏夹API服务
 */

import axiosInstance from './axiosInstance';
import { log } from '../utils/logger';

// 收藏夹相关类型定义
export interface FavoriteFolder {
  id: number;
  name: string;
  display_name: string;
  description?: string;
  color: string;
  icon: string;
  sort_order: number;
  is_default: boolean;
  created_at: string;
  updated_at: string;
  icon_count?: number;
}

export interface FavoriteFolderCreate {
  name: string;
  display_name: string;
  description?: string;
  color?: string;
  icon?: string;
  sort_order?: number;
}

export interface FavoriteFolderUpdate {
  name?: string;
  display_name?: string;
  description?: string;
  color?: string;
  icon?: string;
  sort_order?: number;
}

export interface FavoriteItem {
  id: number;
  favorite_id: number;
  icon_id: number;
  sort_order: number;
  notes?: string;
  created_at: string;
  icon?: any; // IconMetadata
}

export interface FavoriteItemCreate {
  icon_id: number;
  notes?: string;
}

export interface FavoriteItemUpdate {
  sort_order?: number;
  notes?: string;
}

// 收藏夹管理API
export const favoritesApi = {
  // 获取所有收藏夹
  async getFolders(): Promise<FavoriteFolder[]> {
    try {
      log.api('GET', '/favorites/folders');
      const response = await axiosInstance.get<FavoriteFolder[]>('/favorites/folders');
      log.api('GET', '/favorites/folders', null, response.data);
      return response.data;
    } catch (error) {
      log.error('Failed to fetch favorite folders', error, 'FAVORITES_API');
      throw error;
    }
  },

  // 创建收藏夹
  async createFolder(data: FavoriteFolderCreate): Promise<FavoriteFolder> {
    try {
      log.api('POST', '/favorites/folders', data);
      const response = await axiosInstance.post<FavoriteFolder>('/favorites/folders', data);
      log.api('POST', '/favorites/folders', data, response.data);
      return response.data;
    } catch (error) {
      log.error('Failed to create favorite folder', error, 'FAVORITES_API');
      throw error;
    }
  },

  // 更新收藏夹
  async updateFolder(id: number, data: FavoriteFolderUpdate): Promise<FavoriteFolder> {
    try {
      log.api('PUT', `/favorites/folders/${id}`, data);
      const response = await axiosInstance.put<FavoriteFolder>(`/favorites/folders/${id}`, data);
      log.api('PUT', `/favorites/folders/${id}`, data, response.data);
      return response.data;
    } catch (error) {
      log.error('Failed to update favorite folder', error, 'FAVORITES_API');
      throw error;
    }
  },

  // 删除收藏夹
  async deleteFolder(id: number): Promise<void> {
    try {
      log.api('DELETE', `/favorites/folders/${id}`);
      await axiosInstance.delete(`/favorites/folders/${id}`);
      log.api('DELETE', `/favorites/folders/${id}`, null, 'success');
    } catch (error) {
      log.error('Failed to delete favorite folder', error, 'FAVORITES_API');
      throw error;
    }
  },

  // 获取收藏夹中的图标
  async getFolderIcons(
    folderId: number,
    page: number = 1,
    pageSize: number = 50,
    search?: string
  ): Promise<{
    icons: FavoriteItem[];
    total: number;
    page: number;
    page_size: number;
    total_pages: number;
  }> {
    try {
      const params = { page, page_size: pageSize, search };
      log.api('GET', `/favorites/folders/${folderId}/icons`, params);
      const response = await axiosInstance.get(`/favorites/folders/${folderId}/icons`, { params });
      log.api('GET', `/favorites/folders/${folderId}/icons`, params, response.data);
      return response.data;
    } catch (error) {
      log.error('Failed to fetch folder icons', error, 'FAVORITES_API');
      throw error;
    }
  },

  // 添加图标到收藏夹
  async addIconToFolder(folderId: number, data: FavoriteItemCreate): Promise<FavoriteItem> {
    try {
      log.api('POST', `/favorites/folders/${folderId}/icons`, data);
      const response = await axiosInstance.post<FavoriteItem>(`/favorites/folders/${folderId}/icons`, data);
      log.api('POST', `/favorites/folders/${folderId}/icons`, data, response.data);
      return response.data;
    } catch (error) {
      log.error('Failed to add icon to folder', error, 'FAVORITES_API');
      throw error;
    }
  },

  // 从收藏夹移除图标
  async removeIconFromFolder(folderId: number, iconId: number): Promise<void> {
    try {
      log.api('DELETE', `/favorites/folders/${folderId}/icons/${iconId}`);
      await axiosInstance.delete(`/favorites/folders/${folderId}/icons/${iconId}`);
      log.api('DELETE', `/favorites/folders/${folderId}/icons/${iconId}`, null, 'success');
    } catch (error) {
      log.error('Failed to remove icon from folder', error, 'FAVORITES_API');
      throw error;
    }
  },

  // 更新收藏项
  async updateFavoriteItem(folderId: number, iconId: number, data: FavoriteItemUpdate): Promise<FavoriteItem> {
    try {
      log.api('PUT', `/favorites/folders/${folderId}/icons/${iconId}`, data);
      const response = await axiosInstance.put<FavoriteItem>(`/favorites/folders/${folderId}/icons/${iconId}`, data);
      log.api('PUT', `/favorites/folders/${folderId}/icons/${iconId}`, data, response.data);
      return response.data;
    } catch (error) {
      log.error('Failed to update favorite item', error, 'FAVORITES_API');
      throw error;
    }
  },

  // 获取图标所在的收藏夹列表
  async getIconFolders(iconId: number): Promise<FavoriteFolder[]> {
    try {
      log.api('GET', `/favorites/icons/${iconId}/folders`);
      const response = await axiosInstance.get<FavoriteFolder[]>(`/favorites/icons/${iconId}/folders`);
      log.api('GET', `/favorites/icons/${iconId}/folders`, null, response.data);
      return response.data;
    } catch (error) {
      log.error('Failed to fetch icon folders', error, 'FAVORITES_API');
      throw error;
    }
  },

  // 批量操作
  async batchMoveIcons(fromFolderId: number, toFolderId: number, iconIds: number[]): Promise<{
    success: number;
    failed: number;
    results: Array<{ iconId: number; success: boolean; error?: string }>;
  }> {
    try {
      const data = { from_folder_id: fromFolderId, to_folder_id: toFolderId, icon_ids: iconIds };
      log.api('POST', '/favorites/batch/move', data);
      const response = await axiosInstance.post('/favorites/batch/move', data);
      log.api('POST', '/favorites/batch/move', data, response.data);
      return response.data;
    } catch (error) {
      log.error('Failed to batch move icons', error, 'FAVORITES_API');
      throw error;
    }
  },

  async batchDeleteIcons(folderId: number, iconIds: number[]): Promise<{
    success: number;
    failed: number;
    results: Array<{ iconId: number; success: boolean; error?: string }>;
  }> {
    try {
      const data = { folder_id: folderId, icon_ids: iconIds };
      log.api('POST', '/favorites/batch/delete', data);
      const response = await axiosInstance.post('/favorites/batch/delete', data);
      log.api('POST', '/favorites/batch/delete', data, response.data);
      return response.data;
    } catch (error) {
      log.error('Failed to batch delete icons', error, 'FAVORITES_API');
      throw error;
    }
  }
};

export default favoritesApi;
