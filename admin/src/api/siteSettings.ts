import axiosInstance from './axiosInstance';

// 个人信息配置类型
export interface PersonalInfoConfig {
  name: string;
  headline: string;
  introduction: string;
}

// 主题颜色配置类型
export interface ThemeColorsConfig {
  primary: string;
  background: string;
  foreground: string;
  card: string;
  card_foreground: string;
  secondary: string;
  secondary_foreground: string;
  muted: string;
  muted_foreground: string;
  border: string;
  radius: string;
}

// 社交链接类型
export interface SocialLinkItem {
  name: string;
  icon: string;
  href: string;
}

// 首页各部分配置类型
export interface HomepageSectionsConfig {
  projectHeadLine: string;
  projectIntro: string;
  blogHeadLine: string;
  blogIntro: string;
  activityHeadLine: string;
  activityIntro: string;
}

// API响应类型
export interface PersonalInfoResponse {
  personal_info: PersonalInfoConfig;
}

export interface ThemeColorsResponse {
  theme_colors: ThemeColorsConfig;
}

export interface SocialLinksResponse {
  social_links: SocialLinkItem[];
}

export interface TechIconItem {
  key: string;
  library?: string;
  visible: boolean;
  order: number;
}

export interface TechIconsResponse {
  tech_icons: TechIconItem[];
}

export interface TechIconDisplayConfig {
  rotationSpeed: number;
  autoRotate: boolean;
  showTooltips: boolean;
  iconSize: number;
  sphereRadius: number;
  maxIcons: number;
}

export interface TechIconDisplayConfigResponse {
  display_config: TechIconDisplayConfig;
}

export interface HomepageSectionsResponse {
  homepage_sections: HomepageSectionsConfig;
}

// 页面配置类型
export interface PageConfig {
  title: string;
  description: string;
}

export interface PagesConfig {
  blogs: PageConfig;
  projects: PageConfig;
  gallery: PageConfig;
}

export interface PagesConfigResponse {
  pages_config: PagesConfig;
}

// 个人信息API
export const getPersonalInfo = async (): Promise<PersonalInfoResponse> => {
  const response = await axiosInstance.get<PersonalInfoResponse>('/site-settings/personal-info/config');
  return response.data;
};

export const updatePersonalInfo = async (personalInfo: PersonalInfoConfig): Promise<PersonalInfoResponse> => {
  const response = await axiosInstance.put<PersonalInfoResponse>('/site-settings/personal-info/config', personalInfo);
  return response.data;
};

// 主题颜色API
export const getThemeColors = async (): Promise<ThemeColorsResponse> => {
  const response = await axiosInstance.get<ThemeColorsResponse>('/site-settings/theme-colors/config');
  return response.data;
};

export const updateThemeColors = async (themeColors: ThemeColorsConfig): Promise<ThemeColorsResponse> => {
  const response = await axiosInstance.put<ThemeColorsResponse>('/site-settings/theme-colors/config', themeColors);
  return response.data;
};

// 社交链接API
export const getSocialLinks = async (): Promise<SocialLinksResponse> => {
  const response = await axiosInstance.get<SocialLinksResponse>('/site-settings/social-links/config');
  return response.data;
};

export const updateSocialLinks = async (socialLinks: SocialLinkItem[]): Promise<SocialLinksResponse> => {
  const response = await axiosInstance.put<SocialLinksResponse>('/site-settings/social-links/config', socialLinks);
  return response.data;
};

// 技术栈图标API
export const getTechIcons = async (): Promise<TechIconsResponse> => {
  const response = await axiosInstance.get<TechIconsResponse>('/site-settings/tech-icons/config');
  return response.data;
};

export const updateTechIcons = async (techIcons: TechIconItem[]): Promise<TechIconsResponse> => {
  const response = await axiosInstance.put<TechIconsResponse>('/site-settings/tech-icons/config', techIcons);
  return response.data;
};

// 技术栈图标显示配置API
export const getTechIconDisplayConfig = async (): Promise<TechIconDisplayConfigResponse> => {
  const response = await axiosInstance.get<TechIconDisplayConfigResponse>('/site-settings/tech-icons/display-config');
  return response.data;
};

export const updateTechIconDisplayConfig = async (displayConfig: TechIconDisplayConfig): Promise<TechIconDisplayConfigResponse> => {
  const response = await axiosInstance.put<TechIconDisplayConfigResponse>('/site-settings/tech-icons/display-config', displayConfig);
  return response.data;
};

// 首页各部分配置API
export const getHomepageSections = async (): Promise<HomepageSectionsResponse> => {
  const response = await axiosInstance.get<HomepageSectionsResponse>('/site-settings/homepage-sections/config');
  return response.data;
};

export const updateHomepageSections = async (homepageSections: HomepageSectionsConfig): Promise<HomepageSectionsResponse> => {
  const response = await axiosInstance.put<HomepageSectionsResponse>('/site-settings/homepage-sections/config', homepageSections);
  return response.data;
};

// 页面配置API
export const getPagesConfig = async (): Promise<PagesConfigResponse> => {
  const response = await axiosInstance.get<PagesConfigResponse>('/site-settings/pages-config');
  return response.data;
};

export const updatePagesConfig = async (pagesConfig: PagesConfig): Promise<PagesConfigResponse> => {
  const response = await axiosInstance.put<PagesConfigResponse>('/site-settings/pages-config', pagesConfig);
  return response.data;
};


