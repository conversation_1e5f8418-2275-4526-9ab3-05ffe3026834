import axios from 'axios';
import { useAuthStore } from '../store/authStore'; // 使用相对路径引入

// 从环境变量读取后端 API 的基础 URL，如果环境变量未设置，则使用默认值
// 注意：确保 .env 文件中的 VITE_API_BASE_URL 指向正确的后端地址
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api'; // 使用代理路径

// 调试信息（开发环境）
if (process.env.NODE_ENV === 'development') {
  console.log('[AXIOS] Environment VITE_API_BASE_URL:', import.meta.env.VITE_API_BASE_URL);
  console.log('[AXIOS] Final API_BASE_URL:', API_BASE_URL);
}

const axiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
axiosInstance.interceptors.request.use(
  (config) => {
    const token = useAuthStore.getState().token; // 从 Zustand 获取 token
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // 如果是FormData，删除Content-Type让axios自动设置
    if (config.data instanceof FormData) {
      delete config.headers['Content-Type'];
    }

    // 记录请求开始时间用于性能监控
    config.metadata = { startTime: Date.now() };

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
axiosInstance.interceptors.response.use(
  (response) => {
    // 计算请求耗时并记录性能日志
    const config = response.config as any;
    if (config.metadata?.startTime) {
      const duration = Date.now() - config.metadata.startTime;
      const url = config.url || '';
      const method = config.method?.toUpperCase() || 'GET';

      // 记录API性能日志
      if (duration > 1000) {
        console.warn(`[API SLOW] ${method} ${url} took ${duration}ms`);
      }
    }

    return response;
  },
  (error) => {
    // 计算失败请求的耗时
    const config = error.config as any;
    if (config?.metadata?.startTime) {
      const duration = Date.now() - config.metadata.startTime;
      const url = config.url || '';
      const method = config.method?.toUpperCase() || 'GET';
      console.error(`[API ERROR] ${method} ${url} failed after ${duration}ms`, error);
    }

    if (error.response && error.response.status === 401) {
      console.warn('Unauthorized request - 401');
      useAuthStore.getState().clearAuth(); // 清除认证状态
      // 使用 window.location 重定向到登录页
      // 注意：在 React 组件外，不能直接使用 useNavigate，所以用 window.location
      // 确保登录页路由不在此拦截器影响范围内，避免无限循环
      if (window.location.pathname !== '/login') {
         window.location.href = '/login';
      }
    }
    return Promise.reject(error);
  }
);

export default axiosInstance; 