import axiosInstance from './axiosInstance';

// SEO设置相关接口
export interface SeoSetting {
  id: number;
  setting_key: string;
  setting_value: any;
  setting_type: 'global' | 'page' | 'content' | 'social' | 'analytics';
  page_type?: string;
  content_id?: number;
  is_active: boolean;
  priority: number;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface SeoSettingCreate {
  setting_key: string;
  setting_value: any;
  setting_type?: string;
  page_type?: string;
  content_id?: number;
  is_active?: boolean;
  priority?: number;
  description?: string;
}

export interface SeoSettingUpdate {
  setting_value?: any;
  setting_type?: string;
  page_type?: string;
  content_id?: number;
  is_active?: boolean;
  priority?: number;
  description?: string;
}

export interface MetaTagsResponse {
  title: string;
  description: string;
  keywords: string;
  canonical_url: string;
  robots: string;
  og_title: string;
  og_description: string;
  og_image: string;
  og_url: string;
  og_type: string;
  og_site_name: string;
  og_locale: string;
  twitter_card: string;
  twitter_site: string;
  twitter_creator: string;
  structured_data?: any;
}

export interface SeoAnalysis {
  page_type: string;
  title_length: number;
  description_length: number;
  keywords_count: number;
  has_og_tags: boolean;
  has_twitter_tags: boolean;
  has_canonical: boolean;
  has_structured_data: boolean;
  recommendations: string[];
  score: number;
  issues: string[];
  warnings: string[];
}

export interface SeoOverview {
  global_config?: any;
  og_config?: any;
  analytics_config?: any;
  page_configs: any[];
  content_templates: any[];
}

// SEO设置API
export const seoApi = {
  // 获取SEO设置列表
  getSeoSettings: async (params?: {
    setting_type?: string;
    page_type?: string;
    is_active?: boolean;
    page?: number;
    size?: number;
  }) => {
    const response = await axiosInstance.get('/seo/settings', { params });
    return response.data;
  },

  // 获取单个SEO设置
  getSeoSetting: async (settingKey: string) => {
    const response = await axiosInstance.get(`/seo/settings/${settingKey}`);
    return response.data;
  },

  // 创建SEO设置
  createSeoSetting: async (data: SeoSettingCreate) => {
    const response = await axiosInstance.post('/seo/settings', data);
    return response.data;
  },

  // 更新SEO设置
  updateSeoSetting: async (settingKey: string, data: SeoSettingUpdate) => {
    const response = await axiosInstance.put(`/seo/settings/${settingKey}`, data);
    return response.data;
  },

  // 删除SEO设置
  deleteSeoSetting: async (settingKey: string) => {
    const response = await axiosInstance.delete(`/seo/settings/${settingKey}`);
    return response.data;
  },

  // 获取SEO概览
  getSeoOverview: async () => {
    const response = await axiosInstance.get('/seo/overview');
    return response.data;
  },

  // 生成meta标签
  generateMetaTags: async (data: {
    page_type: string;
    content_id?: number;
    custom_data?: any;
  }) => {
    const response = await axiosInstance.post('/seo/meta-tags', data);
    return response.data;
  },

  // 获取页面meta标签
  getPageMetaTags: async (pageType: string) => {
    const response = await axiosInstance.get(`/seo/meta-tags/page/${pageType}`);
    return response.data;
  },

  // 获取内容meta标签
  getContentMetaTags: async (pageType: string, contentId: number) => {
    const response = await axiosInstance.get(`/seo/meta-tags/content/${pageType}/${contentId}`);
    return response.data;
  },

  // 获取结构化数据
  getGlobalStructuredData: async () => {
    const response = await axiosInstance.get('/seo/structured-data/global');
    return response.data;
  },

  getBlogStructuredData: async (blogSlug: string) => {
    const response = await axiosInstance.get(`/seo/structured-data/blog/${blogSlug}`);
    return response.data;
  },

  getProjectStructuredData: async (projectSlug: string) => {
    const response = await axiosInstance.get(`/seo/structured-data/project/${projectSlug}`);
    return response.data;
  },

  // SEO分析
  analyzePageSeo: async (pageType: string) => {
    const response = await axiosInstance.get(`/seo/analysis/page/${pageType}`);
    return response.data;
  },

  // 获取sitemap
  getSitemap: async () => {
    const response = await axiosInstance.get('/seo/sitemap.xml', {
      responseType: 'text'
    });
    return response.data;
  },

  // 获取robots.txt
  getRobots: async () => {
    const response = await axiosInstance.get('/seo/robots.txt', {
      responseType: 'text'
    });
    return response.data;
  },

  // 新增的分析功能
  analyzeContentSeo: async (contentType: string, contentId: number) => {
    const response = await axiosInstance.get(`/seo/analysis/content/${contentType}/${contentId}`);
    return response.data;
  },

  analyzeSitePerformance: async () => {
    const response = await axiosInstance.get('/seo/analysis/site-performance');
    return response.data;
  },

  analyzeKeywords: async (pageType: string) => {
    const response = await axiosInstance.get(`/seo/analysis/keywords/${pageType}`);
    return response.data;
  },

  analyzeImages: async (pageType: string) => {
    const response = await axiosInstance.get(`/seo/analysis/images/${pageType}`);
    return response.data;
  },

  analyzeLinks: async (pageType: string) => {
    const response = await axiosInstance.get(`/seo/analysis/links/${pageType}`);
    return response.data;
  },

  analyzeUrl: async (url: string) => {
    const response = await axiosInstance.post('/seo/analysis/url', { url });
    return response.data;
  },

  validateStructuredData: async (contentType: string, contentId: number) => {
    const response = await axiosInstance.get(`/seo/tools/validate-structured-data/${contentType}/${contentId}`);
    return response.data;
  },

  // 批量SEO优化功能
  batchOptimizeSeo: async (pageTypes: string[]) => {
    const response = await axiosInstance.post('/seo/batch/optimize', { page_types: pageTypes });
    return response.data;
  },

  // 生成SEO报告
  generateSeoReport: async (options?: {
    include_pages?: boolean;
    include_content?: boolean;
    include_performance?: boolean;
    format?: 'json' | 'pdf';
  }) => {
    const response = await axiosInstance.post('/seo/reports/generate', options || {});
    return response.data;
  },

  // 验证所有SEO配置
  validateAllSeoConfigs: async () => {
    const response = await axiosInstance.get('/seo/validation/all');
    return response.data;
  },

  // 获取SEO优化建议
  getSeoRecommendations: async (pageType?: string) => {
    const params = pageType ? { page_type: pageType } : {};
    const response = await axiosInstance.get('/seo/recommendations', { params });
    return response.data;
  }
};

export default seoApi;
