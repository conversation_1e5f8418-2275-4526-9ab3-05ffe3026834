import axiosInstance from './axiosInstance';
import { log } from '../utils/logger';

// Define the expected response structure (can be imported from types later if needed)
export interface StatsSummaryResponse {
  blog_count: number;
  project_count: number;
  image_count: number;
  user_count: number;
}

export const getStatsSummary = async (): Promise<StatsSummaryResponse> => {
  log.api('GET', '/stats/summary');
  const response = await axiosInstance.get<StatsSummaryResponse>('/stats/summary');
  log.api('GET', '/stats/summary', null, response.data);
  return response.data;
};