import axiosInstance from './axiosInstance';

export interface WalineConfig {
  server_url: string;
  admin_url: string;
}

export interface WalineConfigRequest {
  server_url: string;
  admin_url?: string;
}

export interface PublicWalineConfig {
  server_url: string;
}

// 获取Waline配置（管理员用）
export const getWalineConfig = async (): Promise<WalineConfig> => {
  const response = await axiosInstance.get('/waline/config');
  return response.data;
};

// 设置Waline配置（管理员用）
export const setWalineConfig = async (config: WalineConfigRequest): Promise<void> => {
  await axiosInstance.post('/waline/config', config);
};

// 获取公共Waline配置（前端用）
export const getPublicWalineConfig = async (): Promise<PublicWalineConfig> => {
  const response = await axiosInstance.get('/public/waline/config');
  return response.data;
};
