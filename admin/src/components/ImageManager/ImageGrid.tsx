import React, { memo, useMemo, useCallback } from 'react';
import {
  Row, Col, Card, Checkbox, Button, Space, Tag, Tooltip,
  Typography, Image, List, Avatar, Dropdown, Menu, Modal
} from 'antd';
import {
  EyeOutlined, EditOutlined, DeleteOutlined, DownloadOutlined,
  MoreOutlined, CopyOutlined, ShareAltOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import 'dayjs/locale/zh-cn';

dayjs.extend(relativeTime);
dayjs.locale('zh-cn');

const { Text, Paragraph } = Typography;
const { Meta } = Card;

interface ImageData {
  id: number;
  url: string;
  thumbnail_url?: string;
  display_name?: string;
  description?: string;
  original_filename: string;
  file_size: number;
  mime_type: string;
  width: number;
  height: number;
  category?: any;
  tags: any[];
  usage_type: string;
  is_public: boolean;
  created_at: string;
  view_count: number;
  download_count: number;
}

interface ImageGridProps {
  images: ImageData[];
  viewMode: 'grid' | 'list';
  selectedImages: number[];
  onImageSelect: (imageId: number, selected: boolean) => void;
  onImagePreview: (image: ImageData) => void;
  onImageEdit: (image: ImageData) => void;
  onImageDelete?: (imageId: number) => void;
}

const ImageGrid: React.FC<ImageGridProps> = ({
  images,
  viewMode,
  selectedImages,
  onImageSelect,
  onImagePreview,
  onImageEdit,
  onImageDelete
}) => {
  // 使用useMemo缓存计算结果
  const formatFileSize = useMemo(() => (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }, []);

  const getImageUrl = useCallback((image: ImageData): string => {
    const baseUrl = 'http://**************:8000';
    return image.thumbnail_url
      ? `${baseUrl}${image.thumbnail_url}`
      : `${baseUrl}${image.url}`;
  }, []);

  const getFullImageUrl = useCallback((image: ImageData): string => {
    const baseUrl = 'http://**************:8000';
    return `${baseUrl}${image.url}`;
  }, []);

  const handleCopyUrl = useCallback((image: ImageData) => {
    const url = getFullImageUrl(image);
    navigator.clipboard.writeText(url);
    // message.success('图片链接已复制到剪贴板');
  }, [getFullImageUrl]);

  const handleDownload = useCallback((image: ImageData) => {
    const url = getFullImageUrl(image);
    const link = document.createElement('a');
    link.href = url;
    link.download = image.display_name || image.original_filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }, [getFullImageUrl]);

  const getActionMenu = (image: ImageData) => (
    <Menu>
      <Menu.Item
        key="preview"
        icon={<EyeOutlined />}
        onClick={() => onImagePreview(image)}
      >
        预览
      </Menu.Item>
      <Menu.Item
        key="edit"
        icon={<EditOutlined />}
        onClick={() => onImageEdit(image)}
      >
        编辑
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item
        key="copy"
        icon={<CopyOutlined />}
        onClick={() => handleCopyUrl(image)}
      >
        复制链接
      </Menu.Item>
      <Menu.Item
        key="download"
        icon={<DownloadOutlined />}
        onClick={() => handleDownload(image)}
      >
        下载
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item
        key="delete"
        icon={<DeleteOutlined />}
        danger
        onClick={() => {
          onImageDelete?.(image.id);
        }}
      >
        删除
      </Menu.Item>
    </Menu>
  );

  const renderGridView = () => (
    <Row gutter={[16, 16]}>
      {images.map((image) => (
        <Col key={image.id} xs={24} sm={12} md={8} lg={6} xl={4}>
          <Card
            hoverable
            style={{ height: '100%' }}
            cover={
              <div style={{ position: 'relative', height: 200, overflow: 'hidden' }}>
                <Image
                  src={getImageUrl(image)}
                  alt={image.display_name || image.original_filename}
                  style={{ 
                    width: '100%', 
                    height: '100%', 
                    objectFit: 'cover',
                    cursor: 'pointer'
                  }}
                  preview={false}
                  onClick={() => onImagePreview(image)}
                />
                
                {/* 选择框 */}
                <Checkbox
                  checked={selectedImages.includes(image.id)}
                  onChange={(e) => onImageSelect(image.id, e.target.checked)}
                  style={{
                    position: 'absolute',
                    top: 8,
                    left: 8,
                    zIndex: 1
                  }}
                />

                {/* 操作按钮 */}
                <div
                  style={{
                    position: 'absolute',
                    top: 8,
                    right: 8,
                    zIndex: 1
                  }}
                >
                  <Dropdown overlay={getActionMenu(image)} trigger={['click']}>
                    <Button
                      type="primary"
                      size="small"
                      icon={<MoreOutlined />}
                      style={{ opacity: 0.8 }}
                    />
                  </Dropdown>
                </div>

                {/* 状态标签 */}
                <div
                  style={{
                    position: 'absolute',
                    bottom: 8,
                    left: 8,
                    right: 8,
                    zIndex: 1
                  }}
                >
                  <Space wrap>
                    {!image.is_public && (
                      <Tag color="orange" size="small">私有</Tag>
                    )}
                    {image.usage_type !== 'general' && (
                      <Tag color="blue" size="small">{image.usage_type}</Tag>
                    )}
                    {image.category && (
                      <Tag color={image.category.color} size="small">
                        {image.category.name}
                      </Tag>
                    )}
                  </Space>
                </div>
              </div>
            }
            actions={[
              <Tooltip title="预览">
                <EyeOutlined onClick={() => onImagePreview(image)} />
              </Tooltip>,
              <Tooltip title="编辑">
                <EditOutlined onClick={() => onImageEdit(image)} />
              </Tooltip>,
              <Tooltip title="下载">
                <DownloadOutlined onClick={() => handleDownload(image)} />
              </Tooltip>
            ]}
          >
            <Meta
              title={
                <Tooltip title={image.display_name || image.original_filename}>
                  <Paragraph
                    ellipsis={{ rows: 1 }}
                    style={{ margin: 0, fontSize: '14px' }}
                  >
                    {image.display_name || image.original_filename}
                  </Paragraph>
                </Tooltip>
              }
              description={
                <div>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {image.width} × {image.height}
                  </Text>
                  <br />
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {formatFileSize(image.file_size)}
                  </Text>
                </div>
              }
            />
          </Card>
        </Col>
      ))}
    </Row>
  );

  const renderListView = () => (
    <List
      itemLayout="horizontal"
      dataSource={images}
      renderItem={(image) => (
        <List.Item
          actions={[
            <Checkbox
              checked={selectedImages.includes(image.id)}
              onChange={(e) => onImageSelect(image.id, e.target.checked)}
            />,
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => onImagePreview(image)}
            />,
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => onImageEdit(image)}
            />,
            <Dropdown overlay={getActionMenu(image)} trigger={['click']}>
              <Button type="text" icon={<MoreOutlined />} />
            </Dropdown>
          ]}
        >
          <List.Item.Meta
            avatar={
              <Avatar
                size={64}
                shape="square"
                src={getImageUrl(image)}
                style={{ cursor: 'pointer' }}
                onClick={() => onImagePreview(image)}
              />
            }
            title={
              <Space>
                <Text strong>{image.display_name || image.original_filename}</Text>
                {!image.is_public && <Tag color="orange" size="small">私有</Tag>}
                {image.usage_type !== 'general' && (
                  <Tag color="blue" size="small">{image.usage_type}</Tag>
                )}
                {image.category && (
                  <Tag color={image.category.color} size="small">
                    {image.category.name}
                  </Tag>
                )}
              </Space>
            }
            description={
              <div>
                {image.description && (
                  <Paragraph
                    ellipsis={{ rows: 2 }}
                    style={{ marginBottom: 8 }}
                  >
                    {image.description}
                  </Paragraph>
                )}
                <Space split={<span>•</span>}>
                  <Text type="secondary">
                    {image.width} × {image.height}
                  </Text>
                  <Text type="secondary">
                    {formatFileSize(image.file_size)}
                  </Text>
                  <Text type="secondary">
                    {image.mime_type}
                  </Text>
                  <Text type="secondary">
                    {dayjs(image.created_at).fromNow()}
                  </Text>
                  <Text type="secondary">
                    查看 {image.view_count} 次
                  </Text>
                </Space>
                {image.tags.length > 0 && (
                  <div style={{ marginTop: 8 }}>
                    {image.tags.map(tag => (
                      <Tag key={tag.id} size="small">
                        {tag.name}
                      </Tag>
                    ))}
                  </div>
                )}
              </div>
            }
          />
        </List.Item>
      )}
    />
  );

  return viewMode === 'grid' ? renderGridView() : renderListView();
};

// 使用memo优化性能，避免不必要的重渲染
export default memo(ImageGrid, (prevProps, nextProps) => {
  // 自定义比较函数，只在关键属性变化时重新渲染
  return (
    prevProps.viewMode === nextProps.viewMode &&
    prevProps.selectedImages.length === nextProps.selectedImages.length &&
    prevProps.images.length === nextProps.images.length &&
    JSON.stringify(prevProps.selectedImages) === JSON.stringify(nextProps.selectedImages)
  );
});
